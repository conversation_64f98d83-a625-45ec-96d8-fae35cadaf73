{"messageError": {"createColorProfile": {"message": "Create Color Profile Failed", "description": ""}, "maxLength": {"message": "{{name}} is too long, max length is {{maxLength}} characters", "description": ""}, "promotionPoolDeactivated": {"message": "This promotion pool is deactivated", "description": ""}, "BOArchive": {"message": "This BO is archive", "description": ""}, "BODelete": {"message": "This BO does not exist", "description": ""}, "attributeArchive": {"message": "This attribute is not available", "description": ""}, "attributeDelete": {"message": "This attribute does not exist", "description": ""}, "collectionArchive": {"message": "This collection is not available", "description": ""}, "collectionDisable": {"message": "This collection is disabled", "description": ""}, "collectionDelete": {"message": "This collection does not exist", "description": ""}, "blockError": {"message": "There are some blocks that are failing. Please check again", "description": ""}, "boError": {"message": "Content sources has a problem. Please check again", "description": ""}, "fieldIsRequired": {"message": "This field is required", "description": ""}, "completeSetupInContentSources": {"message": "Please complete the setup in Content sources", "description": ""}, "fieldEmpty": {"message": "This field can't be empty"}, "nameExisted": {"message": "This name has already existed"}, "richMenuSwitchNoExist": {"message": "There must be at least 1 Switch Menu action"}}, "excluded": {"title": "Excluded", "description": ""}, "useTemplate": {"title": "Use Template", "description": ""}, "all": {"title": "All"}, "noTemplatesFound": {"title": "No Templates Found", "description": ""}, "switchMobileMode": {"title": "Switch Mobile Mode", "description": ""}, "switchDesktopMode": {"title": "Switch Desktop Mode", "description": ""}, "table": {"pagination": {"jumper": "Go to page", "sizeChanger": "Show rows"}, "rowSelected": {"title": "{{numRows}} selected"}}, "row": {"title": "Row", "description": ""}, "selectDate": {"title": "Select date", "description": ""}, "searchForIcon": {"title": "Search for icon", "description": ""}, "standards": {"title": "Standards", "description": ""}, "ok": {"title": "OK", "description": ""}, "height": {"title": "Height", "description": ""}, "width": {"title": "<PERSON><PERSON><PERSON>"}, "unitStyling": {"title": "Unit Styling", "description": ""}, "numberStyling": {"title": "Number Styling", "description": ""}, "minimumWidth": {"title": "Minimum Width", "description": ""}, "countdownStyling": {"title": "Countdown Styling", "description": ""}, "endDate": {"title": "End Date", "description": ""}, "endTime": {"title": "End Time", "description": ""}, "noAction": {"title": "No action", "description": ""}, "restartCountdown": {"title": "Restart countdown", "description": ""}, "unitDisplayType": {"title": "Unit Display Type", "description": ""}, "unitDisplayPosition": {"title": "Unit Display Position", "description": ""}, "displayDays": {"title": "Display Days", "description": ""}, "displayHours": {"title": "Display Hours", "description": ""}, "displayMinutes": {"title": "Display Minutes", "description": ""}, "displaySeconds": {"title": "Display Seconds", "description": ""}, "unitDisplaySettings": {"title": "Unit Display Settings", "description": ""}, "hours": {"title": "Hours", "label": "Hours label", "description": ""}, "minutes": {"title": "Minutes", "label": "Minutes label", "description": ""}, "seconds": {"title": "Seconds", "label": "Seconds label", "unit": "second(s)", "description": ""}, "overline": {"title": "Overline", "description": ""}, "lineThrough": {"title": "Line-Through", "description": ""}, "underline": {"title": "Underline", "description": ""}, "capitalize": {"title": "Capitilize"}, "uppercase": {"title": "Uppercase", "description": ""}, "lowercase": {"title": "Lowercase", "description": ""}, "fontFamily": {"title": "Font Family", "description": ""}, "fontSettings": {"title": "Font Settings", "description": ""}, "opacity": {"title": "Opacity", "description": ""}, "imageStyling": {"title": "Image Styling", "description": ""}, "uploadedAt": {"title": "Uploaded: {{date}} at {{time}}", "description": ""}, "clickToAddColumn": {"title": "Click to start adding column blocks", "description": ""}, "searchImage": {"title": "Search image", "description": ""}, "column": {"title": "Column", "description": ""}, "searchBlock": {"title": "Search block...", "description": ""}, "text": {"title": "Text", "description": ""}, "image": {"title": "Image", "description": ""}, "applyImageAll": {"title": "Apply image all", "description": ""}, "cellImage": {"title": "Cell Image", "description": ""}, "replaceImage": {"title": "Replace Image", "description": ""}, "cellStyling": {"title": "Cell Styling", "description": ""}, "imageURL": {"title": "Image URL", "placeholder": "Enter image URL"}, "clickToAddImage": {"title": "Click to add an image", "description": ""}, "deleteImage": {"title": "Delete Image", "description": "Are you sure you want to delete the image {{name}}"}, "button": {"title": "<PERSON><PERSON>", "size": {"xSmall": "X Small", "small": "Small", "medium": "Medium", "large": "Large", "xLarge": "X Large", "x2Large": "X2 Large"}, "description": ""}, "buttonStyling": {"title": "Button Styling", "description": ""}, "optinFields": {"title": "Optin fields", "description": "", "fields": {"firstNameField": {"title": "First Name", "placeholder": "Enter your first name here..."}, "lastNameField": {"title": "Last Name", "placeholder": "Enter your last name here..."}, "nameField": {"title": "Name", "placeholder": "Enter your name here..."}, "emailField": {"title": "Email", "placeholder": "Enter your email address here..."}, "phoneField": {"title": "Phone", "placeholder": "Enter your phone number here..."}, "privacyNotice": {"title": "Privacy Notice"}, "dropdownSelect": {"title": "Dropdown Select", "placeholder": "Please select..."}, "datetime": {"title": "Datetime", "placeholder": "Please select date..."}, "inputField": {"title": "Text", "placeholder": "Enter your text"}, "inputAreaField": {"title": "Text Area", "placeholder": "Enter your text area"}, "numberField": {"title": "Number", "placeholder": "Enter your number"}, "radioButton": {"title": "Radio Button", "placeholder": "Enter your number"}, "checkbox": {"title": "Checkbox", "placeholder": ""}, "rating": {"title": "Feedback Rating", "name": "Rating", "label": "Rating", "placeholder": "", "emotion": {"extremelyDissatisfied": "Extremely Dissatisfied", "veryDissatisfied": "Very Dissatisfied", "dissatisfied": "Dissatisfied", "slightlyDissatisfied": "Slightly Dissatisfied", "neutral": "Neutral", "slightlySatisfied": "Slightly Satisfied", "satisfied": "Satisfied", "verySatisfied": "Very Satisfied", "extremelySatisfied": "Extremely Satisfied", "delighted": "Delighted"}, "star": {"poor": "Poor", "fair": "Fair", "good": "Good", "veryGood": "Very Good", "excellent": "Excellent"}, "yesno": {"yes": "Yes", "no": "No"}}}}, "cappingLevel": {"title": "Capping level", "description": "", "level": {"variant": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "campaign": {"title": "Campaign", "description": ""}, "journey": {"title": "Journey", "description": ""}}}, "hour": {"title": "this hour", "description": ""}, "day": {"title": "this day", "description": ""}, "week": {"title": "this week", "description": ""}, "month": {"title": "this month", "description": ""}, "lifeTime": {"title": "lifetime", "description": ""}, "yesNo": {"title": "Yes/No", "description": ""}, "countDown": {"title": "Count down", "description": ""}, "video": {"title": "Video", "description": ""}, "couponWheel": {"title": "Coupon wheel", "description": ""}, "couponWheelSectionError": {"title": "Coupon wheel with invalid section settings", "internalCode": {"invalid": "Invalid internal code", "same": "The same internal code exists"}, "limitSpinning": {"invalid": "Invalid limit set value"}, "couponCode": {"required": "Coupon code is required"}, "couponCodeAttr": {"required": "Promotion attribute is required"}}, "limitSpinning": {"title": "<PERSON>it <PERSON>", "description": ""}, "limitRandom": {"title": "<PERSON><PERSON>", "description": ""}, "spacer": {"title": "Spacer", "info": "Block heights will always be a minimum of 30px. You can use margin or padding in the Container Styling of a Block to get smaller spacing between Columns or Blocks.", "description": ""}, "divider": {"title": "Divider", "description": ""}, "group": {"title": "Group", "description": ""}, "addGroup": {"title": "Add Group", "description": ""}, "groupName": {"title": "Group Name", "description": ""}, "icon": {"title": "Icon", "description": ""}, "iconColor": {"title": "Icon Color", "description": ""}, "displayCondition": {"title": "Display condition", "condition": {"title": "Condition"}, "field": {"title": "Field"}, "index": {"title": "Index"}, "operator": {"title": "Operator"}, "value": {"title": "Value"}, "addField": {"title": "Add field"}, "options": {"showWhen": "Show when", "hiddenWhen": "Hidden when", "showAllColumnsWhen": "Show All Columns when", "hideAllColumnsWhen": "Hide All Columns when", "showSlideshowWhen": "Show Slideshow when", "hiddenSlideshowWhen": "Hidden Slideshow when", "showColumnWhen": "Show Column when", "hiddenColumnWhen": "Hidden Column when", "showSlideWhen": "Show Slide when", "hiddenSlideWhen": "Hidden Slide when"}, "description": ""}, "textEditor": {"placeholder": "Enter your text here", "toolbar": {"backgroundColor": {"tooltip": "Background Color"}, "bold": {"tooltip": "Bold"}, "clean": {"tooltip": "Clear Formatting"}, "fontFamily": {"tooltip": "Font Family"}, "fontSize": {"tooltip": "Font Size"}, "fontWeight": {"tooltip": "Font Weight"}, "indentDecrease": {"tooltip": "Decrease Indent"}, "indentIncrease": {"tooltip": "Increase Indent"}, "italic": {"tooltip": "Italic"}, "letterSpacing": {"tooltip": "Letter Spacing"}, "lineHeight": {"tooltip": "Line Height", "options": {"default": "<PERSON><PERSON><PERSON>", "double": "Double", "single": "Single"}}, "link": {"tooltip": "Insert Link"}, "linkEdit": {"tooltip": "Edit Link"}, "linkOpen": {"tooltip": "Open Link"}, "linkRemove": {"tooltip": "Unlink"}, "listBullet": {"tooltip": "Unordered List"}, "listOrdered": {"tooltip": "Ordered List"}, "mergeTags": {"tooltip": "Smart Tags"}, "redo": {"tooltip": "Redo"}, "selectAll": {"tooltip": "Select All"}, "strike": {"tooltip": "Strikethrough"}, "subscript": {"tooltip": "Subscript"}, "superscript": {"tooltip": "Superscript"}, "textAlign": {"tooltip": "Align", "options": {"left": "Align Left", "center": "Align Center", "right": "Align Right", "justify": "Align Justify"}}, "textColor": {"tooltip": "Text Color"}, "textTransform": {"tooltip": "Text Transform", "options": {"capitalize": "Capitalize", "lowercase": "lowercase", "none": "none", "uppercase": "UPPERCASE"}}, "underline": {"tooltip": "Underline"}, "undo": {"tooltip": "Undo"}}}, "html": {"title": "HTML", "description": ""}, "settings": {"title": "Settings", "description": ""}, "blocks": {"title": "Blocks", "description": ""}, "allBlocks": {"title": "All Blocks", "description": ""}, "page": {"title": "Page"}, "pages": {"title": "Pages", "description": ""}, "cancel": {"title": "Cancel"}, "confirm": {"title": "Confirm"}, "save": {"title": "Save"}, "saveChange": {"title": "Save change"}, "popUp": {"title": "Popup", "description": ""}, "floatingBar": {"title": "Floating Bar", "description": ""}, "fullscreen": {"title": "Fullscreen", "description": ""}, "inline": {"title": "Inline", "description": ""}, "slideIn": {"title": "Slide-in", "description": ""}, "gamified": {"title": "Gamified", "description": ""}, "dragBlockHere": {"title": "Drag block here", "description": ""}, "basic": {"title": "Basic"}, "advanced": {"title": "Advanced", "description": ""}, "optinViewStyling": {"title": "Optin View Styling", "description": ""}, "displaySettings": {"title": "Display Settings", "description": ""}, "mode": {"title": "Mode", "description": ""}, "trackingModule": {"title": "Tracking Module", "description": "", "inner": {"source": {"title": "{{prefix}}_Source", "placeholder": "Enter text here..."}, "medium": {"title": "{{prefix}}_Medium", "placeholder": "Enter URL here..."}, "campaign": {"title": "{{prefix}}_Campaign", "placeholder": "Enter text here..."}, "term": {"title": "{{prefix}}_Term", "placeholder": "Enter URL here..."}, "content": {"title": "{{prefix}}_Content", "placeholder": "Enter text here..."}}}, "utmTracking": {"title": "UTM Tracking", "description": "", "inner": {"source": {"title": "Source", "placeholder": "Enter text here..."}, "medium": {"title": "Medium", "placeholder": "Enter URL here..."}, "campaign": {"title": "Campaign", "placeholder": "Enter text here..."}, "term": {"title": "Term", "placeholder": "Enter URL here..."}, "content": {"title": "Content", "placeholder": "Enter text here..."}}}, "templateSettings": {"title": "{{template}} Settings", "description": ""}, "expandEditor": {"title": "Expand editor", "description": ""}, "color": {"title": "Color", "description": ""}, "boxShadow": {"title": "Box Shadow", "description": ""}, "shadowStyle": {"title": "Shadow Style", "description": ""}, "position": {"title": "Position", "description": ""}, "content": {"title": "Content", "description": ""}, "sectionStyling": {"title": "Section styling", "description": ""}, "columnStyling": {"title": "Column {{index}} styling", "description": ""}, "zIndex": {"title": "Z-Index", "description": "", "warning": "<strong>Warning:</strong> Changing z-index values may cause issues interacting with the builder tools."}, "customIdAttribute": {"title": "Custom ID attribute", "description": "", "placeholder": "Custom ID attribute"}, "customClassAttribute": {"title": "Custom Class attribute", "description": "", "placeholder": "Custom Class attribute"}, "toUrl": {"title": "Redirect to a url"}, "openEmail": {"title": "Open email client"}, "clickToCall": {"title": "Click to call"}, "clickCopyText": {"title": "Click to copy text"}, "closeTheCampaign": {"title": "Close the campaign"}, "reLoadPage": {"title": "Reload the page"}, "openWindow": {"title": "Open in the new window"}, "sendOtpAndGoToView": {"title": "Send OTP & Go to View"}, "serviceProviderManagement": {"title": "Service Provider Management", "description": ""}, "selectProvider": {"title": "Select Provider"}, "addNewProvider": {"title": "Add New Provider", "description": ""}, "addNewServiceProvider": {"title": "Add New Service Provider", "description": ""}, "otpForm": {"title": "OTP Form", "description": ""}, "timerLabel": {"title": "Timer Label", "description": ""}, "labelPosition": {"title": "Label Position", "description": ""}, "expiredMessage": {"title": "Expired Message", "description": ""}, "resendButton": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "description": {"title": "Description", "description": ""}, "verifyButton": {"title": "V<PERSON><PERSON>", "description": ""}, "label": {"title": "Label", "description": ""}, "messages": {"title": "Messages", "description": ""}, "message": {"title": "Message", "description": ""}, "failedVerification": {"title": "Failed Verification", "description": ""}, "resentOTP": {"title": "Resent OTP", "description": ""}, "triggerEvent": {"title": "Trigger Event", "description": ""}, "otpFormStyling": {"title": "OTP Form Styling", "description": ""}, "inputFieldStyling": {"title": "Input Field Styling ", "description": ""}, "resendButtonStyling": {"title": "<PERSON>send <PERSON>ton Styling ", "description": ""}, "verifyButtonStyling": {"title": "Verify <PERSON><PERSON> ", "description": ""}, "messageStyling": {"title": "Message Styling ", "description": ""}, "displayIn": {"title": "Display In ", "description": ""}, "countDownColor": {"title": "Countdown Color", "description": ""}, "warnDeleteProvider": {"title": "Are you sure you want to delete this vendor ?", "Description": ""}, "none": {"title": "None"}, "light": {"title": "Light"}, "custom": {"title": "Custom"}, "outside": {"title": "Outside"}, "hidden": {"title": "Hidden"}, "dotted": {"title": "Dotted"}, "dashed": {"title": "Dashed"}, "solid": {"title": "Solid"}, "double": {"title": "Double"}, "groove": {"title": "Groove"}, "ridge": {"title": "Ridge"}, "inset": {"title": "Inset"}, "outset": {"title": "Outset"}, "inside": {"title": "Inside"}, "borderStyle": {"title": "Border Style"}, "border": {"title": "Border"}, "borderColor": {"title": "Border Color"}, "borderWidth": {"title": "Border Width"}, "stretch": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "start": {"title": "Start", "description": ""}, "spaceAround": {"title": "Space Around", "description": ""}, "spaceBetween": {"title": "Space Between", "description": ""}, "end": {"title": "End", "description": ""}, "top": {"title": "Top"}, "right": {"title": "Right"}, "baseline": {"title": "Baseline", "description": ""}, "center": {"title": "Center", "description": ""}, "bottom": {"title": "Bottom"}, "left": {"title": "Left"}, "byField": {"title": "By Field"}, "cornerStyle": {"title": "Corner Style", "description": ""}, "roundedCorners": {"title": "Rounded Corners", "description": ""}, "roundCorner": {"title": "Round Corner", "description": ""}, "topLeft": {"title": "Top left", "description": ""}, "centerLeft": {"title": "Center left", "description": ""}, "centerRight": {"title": "Center right", "description": ""}, "topRight": {"title": "Top right", "description": ""}, "bottomRight": {"title": "Bottom right", "description": ""}, "bottomLeft": {"title": "Bottom left", "description": ""}, "slightlyRound": {"title": "Slightly round", "description": ""}, "capsuleRound": {"title": "Capsule round", "description": ""}, "spacing": {"title": "Spacing", "description": ""}, "innerSpacing": {"title": "Inner Spacing (Padding)", "description": ""}, "outerSpacing": {"title": "Outer Spacing (Margin)", "description": "", "warning": "<strong>Warning:</strong> Using negative margin values may cause issues interacting with the builder tools."}, "gradient": {"title": "Gradient", "description": ""}, "backgroundColor": {"title": "Background color", "description": ""}, "background": {"title": "Background", "description": ""}, "linear": {"title": "Linear", "description": ""}, "radial": {"title": "Radial", "description": ""}, "gradientStyle": {"title": "Gradient Style", "description": ""}, "angle": {"title": "<PERSON><PERSON>", "description": ""}, "gradientColor": {"title": "{{name}} Color", "description": ""}, "gradientLocation": {"title": "{{name}} Color Location", "description": ""}, "firstColor": {"title": "First Color", "description": ""}, "secondColor": {"title": "Second Color", "description": ""}, "firstColorLocation": {"title": "First Color Location", "description": ""}, "secondColorLocation": {"title": "Second Color Location", "description": ""}, "leftTop": {"title": "Left Top", "description": ""}, "leftCenter": {"title": "Left Center", "description": ""}, "leftBottom": {"title": "Left Bottom", "description": ""}, "rightTop": {"title": "Right Top", "description": ""}, "rightCenter": {"title": "Right Center", "description": ""}, "rightBottom": {"title": "Right Bottom", "description": ""}, "centerTop": {"title": "Center Top", "description": ""}, "centerCenter": {"title": "Center Center", "description": ""}, "centerBottom": {"title": "Center Bottom", "description": ""}, "style": {"title": "Style", "description": ""}, "columns": {"title": "Columns", "description": "", "validate": "The total column width (including the column gap) is <strong>{{totalColumnWidth}}%</strong>, which is greater than 100%. This may result in the column gap disappearing, or other unexpected results."}, "columnWidth": {"title": "Column {{index}} width (%)", "description": ""}, "columnGap": {"title": "Column gap (%)", "description": ""}, "buttonText": {"title": "Button Text", "description": "", "placeholder": "Enter text here..."}, "buttonClickAction": {"title": "Button Click Action", "description": ""}, "goView": {"title": "Go to View"}, "closeCampaign": {"title": "Close campaign"}, "with": {"title": "With"}, "buttonSize": {"title": "<PERSON><PERSON>"}, "defaultHover": {"title": "<PERSON><PERSON><PERSON>"}, "hoverStyles": {"title": "Hover Styles"}, "defaultFormErrorStyles": {"title": "Default Form Error Styles"}, "formErrorStyling": {"title": "Form Error Styling"}, "errorPosition": {"title": "Error Position"}, "fontColor": {"title": "Font color"}, "iconSelection": {"title": "Icon Selection", "description": ""}, "imageIconSelection": {"title": "Image/Icon Selection", "description": ""}, "change": {"title": "Change", "description": ""}, "USE": {"title": "USE", "description": ""}, "browseImage": {"title": "Browse Image", "description": "", "warning": "You are using an Image Element with no image. If you no longer wish to have an image, you may want to", "asWell": "as well."}, "deleteImageElement": {"title": "delete the image element", "description": ""}, "imageSelection": {"title": "Image Selection", "description": ""}, "selectImageFromComputer": {"title": "Select Image from computer", "description": ""}, "dragDropFileHere": {"title": "Drag & Drop file here", "description": ""}, "or": {"title": "or", "description": ""}, "globalViewStyling": {"title": "Global View Styling", "description": ""}, "campaignWidth": {"title": "Campaign Width", "description": ""}, "setImagesLayerFirst": {"title": "Set image layer first", "description": ""}, "setImageLazyLoad": {"title": "Set image lazy load", "description": ""}, "hideContainerOverflow": {"title": "Hide container overflow", "description": ""}, "displayYesNoView": {"title": "Display a Yes/No View", "description": ""}, "campaignHasBeenClosed": {"title": "If this campaign <strong>has been closed</strong> but not converted, show it again", "description": ""}, "campaignHasBeenConverted": {"title": "If this campaign <strong>has been converted</strong>, show it again", "description": ""}, "howLowBrowserWillRemember": {"title": "How long the browser will remember a visitor who has seen this campaign.", "description": ""}, "crossSubdomainCookie": {"title": "Cross Subdomain Cookie", "description": ""}, "days": {"title": "Days", "label": "Days label", "description": ""}, "displayPageSlide": {"title": "Display a page slide", "description": ""}, "closeBackgroundClick": {"title": "Close on background click", "description": ""}, "automaticallyClose": {"title": "Automatically close", "description": ""}, "delayShowing": {"title": "Delay showing", "description": ""}, "conditions": {"title": "Conditions", "description": ""}, "percentScroll": {"title": "Percented scroll", "description": ""}, "percent": {"title": "%", "description": ""}, "second": {"title": "second", "description": ""}, "viewPage": {"title": "After viewing page", "description": ""}, "timeToClose": {"title": "Time to close (second)", "description": ""}, "closeTemplate": {"title": "Close Template", "description": ""}, "xColor": {"title": "X color", "description": ""}, "circle": {"title": "Circle", "description": ""}, "square": {"title": "Square", "description": ""}, "buttonStyle": {"title": "Button Style", "description": ""}, "displayCloseButton": {"title": "Display a close button", "description": ""}, "openViewStyling": {"title": "Open View Styling", "description": ""}, "customColors": {"title": "Custom Colors", "description": ""}, "customCSS": {"title": "Custom CSS", "description": "Each of your Custom CSS statements should be on its own line and prefixed with"}, "hoverXColor": {"title": "Hover X color", "description": ""}, "hoverBackgroundColor": {"title": "Hover Background color", "description": ""}, "small": {"title": "Small", "description": ""}, "medium": {"title": "Medium", "description": ""}, "large": {"title": "Large", "description": ""}, "extraLarge": {"title": "Extra Large", "description": ""}, "buttonPosition": {"title": "Button Position", "description": ""}, "xSize": {"title": "X Size", "description": ""}, "xThickness": {"title": "X Thickness", "description": ""}, "thin": {"title": "Thin", "description": ""}, "thick": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "campaignNamespace": {"title": "Campaign Namespace", "description": ""}, "displayMediaTemplateBadge": {"title": "Display MediaTemplate badge", "description": ""}, "enableWebFonts": {"title": "Enable web fonts", "description": ""}, "setImageLayerFirst": {"title": "Set image layer first", "description": ""}, "containerStyle": {"title": "Container <PERSON><PERSON><PERSON>", "description": ""}, "groupStyle": {"title": "Group Styling", "description": ""}, "sortByUploadDate": {"title": "Sort by Upload Date", "description": ""}, "sortBySize": {"title": "Sort by <PERSON><PERSON>", "description": ""}, "size": {"title": "Size", "description": ""}, "icons": {"title": "Icons", "description": ""}, "align": {"title": "Align", "description": ""}, "link": {"title": "Link", "description": ""}, "openInNewWindow": {"title": "Open in New Window", "description": ""}, "addNoFollow": {"title": "Add \"No Follow\"", "description": ""}, "trackClicks": {"title": "Track Clicks", "description": ""}, "containerStyling": {"title": "Container <PERSON><PERSON><PERSON>", "description": ""}, "iconStyling": {"title": "Icon Styling", "description": ""}, "backgroundSpacing": {"title": "Background Spacing", "description": ""}, "successView": {"title": "The Success view"}, "yesnoView": {"title": "The Yes/No view"}, "optinView": {"title": "The Optin view", "description": ""}, "redirectUrl": {"title": "Redirect URL", "warning": "The URL you entered isn’t validating. Try adding <strong>http://</strong> or <strong>https://</strong> to the URL. If you aren’t sure why you are seeing this message, contact support for help."}, "textBlock": {"defaultValue": "<span style=\"font-family: Montserrat; font-size: 22px; letter-spacing: 0px;\">Your Special Bonus Offer Has Been Unlocked</span>"}, "emaiTo": {"title": "Email to", "error": "The email you entered isn’t validating. If you aren’t sure why you are seeing this message, contact support for help."}, "phoneNumber": {"title": "Phone Number"}, "clipBoard": {"title": "Text to copy to clipboard"}, "newWindow": {"title": "New Window URL"}, "iconSize": {"title": "Icon Size (px)", "description": ""}, "spacingPx": {"title": "Spacing (px)", "description": ""}, "displayIconAfterText": {"title": "Display icon after text?", "description": ""}, "enterUrlHere": {"title": "Enter URL here...", "description": ""}, "selectAnIcon": {"title": "Select an icon", "description": ""}, "selectAnIconOrAnImage": {"title": "Select an icon/image", "description": ""}, "titleCountDown": {"title": "Countdown", "description": ""}, "static": {"title": "Static", "description": ""}, "dynamic": {"title": "Dynamic", "description": ""}, "timezone": {"title": "Timezone", "description": ""}, "countdownEndAction": {"title": "Countdown End Action", "description": ""}, "goToView": {"title": "Go to View", "description": ""}, "titleSuccessScript": {"title": "<PERSON> Script", "description": ""}, "titleLimitedSubmit": {"title": "Limited Submit", "description": "", "type": {"unlimited": {"title": "Unlimited frequency", "description": ""}, "limited": {"title": "Limited frequency", "description": ""}}}, "frequency": {"title": "Frequency", "description": ""}, "limMessPosition": {"title": "Limited message position", "description": ""}, "time": {"title": "time(s) per", "description": ""}, "per": {"title": "per", "description": ""}, "descSuccessScript": {"title": "The scripts that will load after the countdown has ended.", "description": ""}, "conversionTracking": {"title": "Conversion tracking?", "description": ""}, "themeColors": {"title": "Theme Colors", "description": ""}, "widthPercent": {"title": "Width (%)", "description": ""}, "inputWidthPercent": {"title": "Input Width (%)", "description": ""}, "fieldWidthPercent": {"title": "<PERSON> Width (%)", "description": ""}, "Placeholder": {"title": "Placeholder", "description": ""}, "fieldName": {"title": "Field Name", "description": "", "placeholder": "Enter field name"}, "fieldLabel": {"title": "Field Label", "description": "", "placeholder": "Enter field label"}, "useCountryCode": {"title": "Use Country Code?", "description": ""}, "countryCodeDefault": {"title": "Country Code Default", "description": ""}, "privacyContent": {"title": "Privacy Content", "description": "", "placeholder": "Enter privacy content"}, "indentation": {"title": "Indentation (px)", "description": ""}, "displayLabel": {"title": "Display Label?", "description": ""}, "rangeNumber": {"title": "Range Number?", "description": ""}, "displayLabels": {"title": "Display Labels?", "description": ""}, "fieldAlignment": {"title": "Field Alignment", "description": ""}, "isNameAlready": {"title": "This name already exist", "description": ""}, "enterYourEmailAddress": {"title": "Enter your email address", "description": ""}, "fieldID": {"title": "Field ID", "description": ""}, "minValue": {"title": "Min Value", "description": ""}, "maxValue": {"title": "Max Value", "description": ""}, "optionColumn": {"title": "Option Column", "description": ""}, "fieldOptions": {"title": "Field Options", "description": ""}, "fieldOptioHead": {"value": {"title": "Value", "description": ""}, "label": {"title": "Label", "description": ""}, "preselect": {"title": "Preselect", "description": ""}, "goToView": {"title": "Go to view", "description": ""}, "removeAll": {"title": "Remove all", "description": ""}}, "addAnOption": {"title": "Add an option"}, "extendValues": {"title": "Extend values", "placeholder": "You can extend values by input or paste values, separated by enter"}, "optionLabelGap": {"title": "Option Label Gap (px)"}, "lineHeightGap": {"title": "Line Height Gap (px)"}, "columnSpacing": {"title": "Column Spacing (px)"}, "optionPosition": {"title": "Option Position", "top": {"title": "Top"}, "right": {"title": "Right"}, "left": {"title": "Left"}, "bottom": {"title": "Bottom"}}, "errorMessage": {"title": "Error Message", "description": "Enter Error Message..."}, "theFieldIsRequired": {"title": "The {{name}} is required.", "description": ""}, "privacyCheckbox": {"title": "Privacy Checkbox?", "description": ""}, "required": {"title": "Required", "description": ""}, "enablePhoneValidation": {"title": "Enable Phone Validation?", "description": ""}, "formFieldStyling": {"title": "Form Field Styling", "description": ""}, "fieldLabelStyling": {"title": "Field Label Styling", "description": ""}, "checkboxRadioButtonStyling": {"title": "Checkbox/ Radio Button Styling", "description": ""}, "submitButtonStyling": {"title": "Submit <PERSON><PERSON>", "description": ""}, "reCaptcha": {"title": "reCAPTCHA", "description": "reCAPTCHA is a free service that protects your website from spam and abuse. reCAPTCHA uses an advanced risk analysis engine and adaptive challenges to keep automated software from engaging in abusive activities on your site. It does this while letting your valid users pass through with ease."}, "formFields": {"title": "Form Fields", "description": ""}, "submitButton": {"title": "Submit <PERSON>", "description": ""}, "textInput": {"title": "Text", "description": ""}, "successScript": {"title": "<PERSON> Scripts", "description": "The scripts that will load after the button has been clicked."}, "addNewField": {"title": "Add New Field", "description": ""}, "allowDisplayInline": {"title": "Allow fields to display inline?", "description": ""}, "margin": {"title": "<PERSON><PERSON>", "description": ""}, "enableReCaptcha": {"title": "Enable Google reCAPTCHA", "description": ""}, "note": {"title": "Note: ", "description": ""}, "isRequired": {"title": "is required"}, "sectionColors": {"title": "Section colors", "description": ""}, "wheelColors": {"title": "Wheel Colors", "description": ""}, "customizeWheelSections": {"title": "Customize Wheel Sections", "description": ""}, "customizeGiftBox": {"title": "Customize Gift Box", "description": ""}, "addSection": {"title": "Add Section", "description": ""}, "addReward": {"title": "Add <PERSON>", "description": ""}, "couponWheelStyling": {"title": "Coupon Wheel Styling", "description": ""}, "outOfCode": {"title": "Out of code", "description": ""}, "setValue": {"title": "Set value", "description": ""}, "unlimited": {"title": "Unlimited", "description": ""}, "wheelWidthPercent": {"title": "Wheel Width (%)", "description": ""}, "pullDirectionPx": {"title": "Pull Direction (px)", "description": ""}, "colorProfileLibrary": {"title": "Color Profile Library", "description": ""}, "saveColorProfile": {"title": "Save Color Profile", "description": ""}, "colorProfileSelection": {"title": "Color profile library selection", "description": ""}, "searchColorProfile": {"title": "Search color profile", "description": ""}, "noData": {"title": "No Data", "description": ""}, "saveNewColorProfile": {"title": "Save a New Color Profile", "description": ""}, "enterNameColorProfile": {"title": "Enter a name below to identify your new color profile", "description": ""}, "colorProfileName": {"title": "Color profile name...", "description": ""}, "saveProfile": {"title": "Save Profile", "description": ""}, "autoApplyCSSPrefixes": {"title": "Automatically apply CSS prefixes", "description": ""}, "customJavascript": {"title": "Custom Javascript", "description": "Custom JavaScript will run after the campaign has been appended to the DOM (om.Html.append.after)."}, "deviceType": {"title": "Device Type", "description": ""}, "templateDetails": {"title": "Template Details", "description": ""}, "templateType": {"title": "Template Type", "description": ""}, "objectiveType": {"title": "Objective", "description": ""}, "saveAs": {"title": "Save as...", "fullTitle": "Save as my template", "myTemplate": "My template", "galleryTemplate": "Template gallery", "newRichMenu": "Save as a new Rich Menu Template", "existingRichMenu": "Save as an existing Rich Menu Template", "description": "Describe your rich menu template"}, "saveAsGallery": {"title": "Save as", "saveAsNewGallery": {"title": "Save as new Gallery Template", "placeholder": "Enter Gallery Template Name", "success": "Save as new Gallery Template Success", "error": "Save as new Gallery Template Error"}, "saveAsExistingGallery": {"title": "Save as existing Gallery Template", "placeholder": "Select Gallery Template", "success": "Save as existing Gallery Template Success", "error": "Save as existing Gallery Template Error"}}, "templateSave": {"templateGallery": "Template Gallery", "saveTemplateGallery": "Save as template gallery"}, "cloneMediaTemplate": {"saveAs": {"title": "Save as", "description": ""}, "saveAsNewTemplate": {"title": "Save as new Media Template", "placeholder": "Enter template name", "description": ""}, "saveTemplateSuccess": {"title": "Save as Media template success", "description": ""}, "saveTemplateFail": {"title": "Save as Media template fail", "description": ""}, "cloneTemplate": {"title": "<PERSON><PERSON> Template", "description": ""}, "cloneTemplateSuccess": {"title": "Clone Template success", "description": ""}, "cloneTemplateFailed": {"title": "Clone Template fail", "description": ""}}, "cloneMediaJson": {"saveAs": {"title": "Save as", "description": ""}, "saveAsNewTemplate": {"title": "Save as new Media json template", "placeholder": "Enter template name", "description": ""}, "saveTemplateSuccess": {"title": "Save as Media json template success", "description": ""}, "saveTemplateFail": {"title": "Save as Media json template fail", "description": ""}, "cloneTemplate": {"title": "<PERSON><PERSON> Template", "description": ""}, "cloneTemplateSuccess": {"title": "Clone Template success", "description": ""}, "cloneTemplateFailed": {"title": "Clone Template fail", "description": ""}}, "desktop": {"title": "Desktop", "description": ""}, "mobile": {"title": "Mobile", "description": "", "warning": "You are currently editing for Mobile devices. Switch to Desktop for all setting options."}, "enableSlideToggle": {"title": "Enable slide toggle", "description": ""}, "collapsedText": {"title": "Collapsed Text", "description": ""}, "loadToggleOpen": {"title": "Load toggle open", "description": ""}, "automaticOpenDelay": {"title": "Automatic open delay", "description": ""}, "loadFloatingBarAtTop": {"title": "Load Floating Bar at top of page", "description": ""}, "enableSmartSuccess": {"title": "Enable Smart Success", "description": ""}, "lockContentBelowCampaign": {"title": "Lock content below campaign", "description": ""}, "enableAttentionActivation": {"title": "Enable Attention Activation", "description": ""}, "contentLockingStyle": {"title": "Content Locking Style", "description": ""}, "remove": {"title": "Remove", "description": ""}, "blur": {"title": "Blur", "description": ""}, "backgroundOverlayColor": {"title": "Background Overlay Color", "description": ""}, "yesNoMutipleStepsCampaigns": {"title": "Yes / No Multi-Step Campaigns", "description": "Multi-step campaigns are one of our highest converting campaign types at MediaTemplate, and it's <strong>proven to increase conversions by as much as 18%.</strong>", "button": "Enable Yes/No for this campaign"}, "importFromOptinView": {"title": "Import from Optin view", "description": ""}, "importFromSuccessView": {"title": "Import from Success view", "description": ""}, "importFromYesNoView": {"title": "Import from Yes/No View", "description": ""}, "yesButton": {"title": "Yes Button", "description": "", "successScript": "The scripts that will load after the <strong>Yes button</strong> has been clicked."}, "noButton": {"title": "No Button", "description": "", "successScript": "The scripts that will load after the <strong>No button</strong> has been clicked."}, "yesButtonStyling": {"title": "Yes Button Styling", "description": ""}, "noButtonStyling": {"title": "No Button Styling", "description": ""}, "reverseButtonOrder": {"title": "Reverse Button Order", "description": ""}, "displayButtonsInline": {"title": "Display Buttons Inline", "description": ""}, "css": {"title": "Css"}, "htmlPanel": {"title": "Html", "description": "The custom HTML can include CSS and JavaScript to be added to your campaign."}, "formShouldBeAdded": {"title": "Forms should be added as a", "description": ""}, "customHtmlIntegration": {"title": "Custom HTML integration"}, "imagePosition": {"title": "Image Position", "description": ""}, "imageRepeat": {"title": "Image Repeat", "description": ""}, "imageSize": {"title": "Image Size", "description": ""}, "repeatVertical": {"title": "Repeat Vertical", "description": ""}, "noRepeat": {"title": "No-repeat", "description": ""}, "repeat": {"title": "Repeat", "description": ""}, "repeatHorizontal": {"title": "Repeat horizontal", "description": ""}, "cover": {"title": "Cover", "description": ""}, "contain": {"title": "Contain", "description": ""}, "auto": {"title": "Auto", "description": ""}, "unset": {"title": "Unset", "description": ""}, "preview": {"title": "Preview", "description": ""}, "devicePreview": {"title": "Device preview", "description": ""}, "manage": {"title": "Manage", "description": ""}, "savedBlocks": {"title": "Saved Blocks", "description": ""}, "saveNewBlock": {"title": "Save a new {{name}} block", "description": "Once saved, you will be able to use this block in the future. It will retain all the settings you have applied."}, "replaceExistingBlock": {"title": "Replace Existing {{name}} Block", "description": "", "warning": "Saving this block will replace your previous save."}, "enterNameBlock": {"title": "Enter a name below to identify your new block.", "placeholder": "Give your block a unique name..."}, "addNote": {"title": "Add a note", "placeholder": "Enter any notes here..."}, "selectValidateRequire": {"title": "{{name}} must be select", "description": ""}, "validateKeyCode": {"title": "Only letters, numbers and underscore are accepted", "description": ""}, "validateRequire": {"title": "{{name}} can't be empty", "description": ""}, "validateMax": {"title": "{{name}} contains no more than {{max}} characters", "description": ""}, "validateDuplicate": {"title": "This name has been used, please try another name.", "description": ""}, "replaceOrCreateNewBlock": {"title": "Replace or Create a New Block", "description": "Choose how you would like to save this block below.", "warning": "This is a previously saved block!"}, "saveAsNew": {"title": "Save As New", "description": ""}, "replaceExisting": {"title": "Replace Existing", "description": ""}, "notes": {"title": "Notes", "description": ""}, "updateBlockDetails": {"title": "Update Block Details", "description": ""}, "saveBlock": {"title": "Save Block", "description": ""}, "deleteSavedBlock": {"title": "Are you sure you want to delete this saved block?", "buttonConfirm": "Confirm Deletion"}, "sortBy": {"title": "Sort by", "description": ""}, "sortOrder": {"title": "Sort order", "description": ""}, "searchSavedBlocks": {"title": "Search saved blocks", "placeholder": "Search saved blocks..."}, "deleteColorProfile": {"title": "Delete Color Profile", "description": "Are you sure you want to delete the profile \"{{name}}\"?"}, "confirmDeletionBlock": {"title": "Confirm deletion of this Block", "description": "Are you sure you want to delete this block?"}, "blockSelectedNotFound": {"title": "Block selected not found", "description": ""}, "fieldsBlockAlreadyUsed": {"title": "The Optin Fields block is already being used", "description": "Each view can only use one Optin Fields block. To add a new Optin Fields block, please first remove the existing one."}, "viewStylingPanel": {"title": "{{viewName}} View Styling", "description": ""}, "switchGlobalStylingModal": {"enableTitle": "Enable Global View Styling?", "disableTitle": "Disable Global View Styling?", "disableDescription": "Craft styles unique to each View of your campaign by disabling Global View Styling. Once disabled, switch to any view to customize that view right here.", "enableDescription": "You are enabling Global View Styling. Once enabled again, you can apply styles to all Views at once. The styles currently in use for each View will continue to be used until you make and save a change."}, "fileType": {"title": "File type", "description": ""}, "fileSize": {"title": "File size", "description": ""}, "under": {"title": "under", "description": ""}, "backgroundImage": {"title": "Background image", "description": ""}, "fileSizeTooBig": {"title": "File size too big", "description": ""}, "invalidFileExtension": {"title": "Invalid file extension", "description": ""}, "notAllowUploadMultipleFile": {"title": "Not allow upload multiple files", "description": ""}, "cannotUploadImage": {"title": "Cannot upload image!", "description": ""}, "itemAlign": {"title": "<PERSON><PERSON>", "description": ""}, "templateNameExisted": {"title": "Template name existed!", "description": ""}, "galleryTemplate": {"startTemplate": "Start with a Template", "templateType": "Template type", "moreTemplate": "More Template", "lessTemplate": "Less Template"}, "createTemplate": {"blank": "Blank Template", "formData": {"templateName": {"label": "Template Name", "errorMessage": {"duplicated": "Template name existed", "required": "Template Name is required"}}, "templateType": {"label": "Template Type", "errorMessage": {}}}, "notification": {"createFailed": "Create Template failed", "createSuccess": "Create Template success"}, "startGallery": "Start with a Gallery", "title": "What type do you want to create?"}, "editTemplate": {"remove": {"message": "Are you sure to remove selected template(s) ?", "notification": {"failed": "Removed failed", "success": "Remove {{numRows}} Template(s) success"}, "title": "Remove {{numRows}} template(s)"}}, "updateTemplate": {"formData": {"templateName": {"label": "Template Name", "errorMessage": {"duplicated": "Template name existed", "required": "Template Name is required"}}}, "notification": {"updateFailed": "Update Template failed", "updateSuccess": "Update Template success"}, "title": "Template"}, "countDownType": {"title": "Countdown Type", "description": "Countdown Type"}, "spread": {"title": "Spread", "description": ""}, "inputFieldSize": {"title": "Input Field Size", "description": ""}, "placeholderColor": {"title": "Placeholder Color", "description": ""}, "fontSize": {"title": "Font size", "description": ""}, "fontWeight": {"title": "Font Weight", "black": "Black", "extraBold": "Extra-Bold", "bold": "Bold", "semiBold": "Semi-Bold", "medium": "Medium", "regular": "Regular", "light": "Light", "extraLight": "Extra-Light", "thin": "Thin"}, "lineHeight": {"title": "Line Height", "description": ""}, "leterSpacing": {"title": "Leter spacing", "description": ""}, "textTransform": {"title": "Text Transform", "description": ""}, "textDecoration": {"title": "Text Decoration", "description": ""}, "fontStyleItalic": {"title": "Font Style Italic", "description": ""}, "close": {"title": "Close", "description": ""}, "successScripts": {"title": "<PERSON> Scripts", "description": "The scripts that will load after the button has been clicked."}, "hostedVideoUrl": {"title": "Hosted Video URL", "description": ""}, "aspectRatio": {"title": "Aspect Ratio", "description": ""}, "clear": {"title": "Clear", "description": ""}, "colorProfile": {"title": "Color Profile", "description": ""}, "edit": {"title": "Edit", "description": ""}, "apply": {"title": "Apply", "description": ""}, "displayStyle": {"title": "Display style", "description": ""}, "slideDirection": {"title": "Slide direction", "description": ""}, "slideUp": {"title": "Slide up", "description": ""}, "slideLeft": {"title": "Slide left", "description": ""}, "slideRight": {"title": "Slide right", "description": ""}, "replace": {"title": "Replace", "description": ""}, "replaceBlock": {"title": "Replace block", "description": ""}, "replaceCell": {"title": "Replace cell", "description": ""}, "bounce": {"title": "<PERSON><PERSON><PERSON>", "description": ""}, "flash": {"title": "Flash", "description": ""}, "pulse": {"title": "Pulse", "description": ""}, "shakeX": {"title": "Shake X", "description": ""}, "shakeY": {"title": "Shake <PERSON>", "description": ""}, "rubberBand": {"title": "Rubber band", "description": ""}, "headShake": {"title": "Head Shake", "description": ""}, "swing": {"title": "Swing", "description": ""}, "tada": {"title": "<PERSON><PERSON>", "description": ""}, "wobble": {"title": "Wobble", "description": ""}, "jello": {"title": "<PERSON><PERSON>", "description": ""}, "heartBeat": {"title": "Heart Beat", "description": ""}, "jackInTheBox": {"title": "Jack In The Box", "description": ""}, "infinite": {"title": "Infinite", "description": ""}, "finite": {"title": "Finite", "description": ""}, "animation": {"title": "Animation", "description": ""}, "animationType": {"title": "Animation Type", "description": ""}, "contentAnimation": {"title": "Content Animation", "description": ""}, "animationDuration": {"title": "Animation Duration", "description": ""}, "animationIterationStyle": {"title": "Iteration Style", "description": ""}, "animationIterationCount": {"title": "Iteration Count", "description": ""}, "toggleAnimation": {"title": "Toggle Animation", "description": ""}, "animationDelay": {"title": "Animation Delay", "description": ""}, "animationWhenClicked": {"title": "Animation When Clicked", "description": ""}, "animationWhenHover": {"title": "Animation When Hover", "description": ""}, "modifiedTime": {"title": "Modified Time", "description": ""}, "enterPlaceholder": {"title": "Enter placeholder"}, "couponCodeIsRequired": {"title": "Coupon code is required", "description": ""}, "maximumFileUpload": {"title": "Maximum number of file to upload is 10!", "description": ""}, "noBlocksFound": {"title": "No blocks found", "description": ""}, "layers": {"title": "Layers", "description": ""}, "components": {"title": "Components", "description": ""}, "addComponent": {"title": "Add Component", "description": ""}, "create": {"title": "Create", "description": ""}, "displayType": {"title": "Display Type", "description": ""}, "delete": {"title": "Delete", "description": ""}, "duplicate": {"title": "Duplicate", "description": ""}, "hide": {"title": "<PERSON>de", "description": ""}, "show": {"title": "Show", "description": ""}, "addColumn": {"title": "Add column", "description": ""}, "couponWheelSetting": {"title": "Coupon wheel setting", "description": ""}, "couponSetting": {"title": "Coupon setting", "description": ""}, "allAvailableCodeHasBeenAllocated": {"title": "All available codes have been allocated, please try again later.", "description": ""}, "field": {"title": "Field", "description": ""}, "index": {"title": "Index", "description": ""}, "selectField": {"title": "Select Field", "description": ""}, "selectIndex": {"title": "Select Index", "description": ""}, "altText": {"title": "Alt Text", "description": ""}, "businessObject": {"title": "Business Object", "description": ""}, "businessObjectSettings": {"title": "Business Object Settings", "description": "", "useOfTemplate": "Use Business Object of Template?"}, "contentSources": {"title": "Content Sources", "description": "", "errorMessage": {"groupSameName": "Content source groups can't have the same name", "groupNameEmpty": " Group name of Content source can't be empty"}}, "level": {"title": "Level", "description": ""}, "selectContentSource": {"title": "Select Content source", "description": ""}, "filter": {"title": "Filter", "description": ""}, "filters": {"title": "Filters", "description": ""}, "ranking": {"title": "Ranking", "description": ""}, "customRanking": {"title": "Custom Ranking", "description": ""}, "algorithms": {"title": "Algorithms", "description": ""}, "addAlgorithms": {"title": "Add Algorithms", "description": ""}, "selectAlgorithms": {"title": "Select Algorithms", "description": ""}, "multiSelectAlgorithms": {"title": "Multi-select Algorithms", "description": ""}, "and": {"title": "And", "description": ""}, "selectEvent": {"title": "Select event", "description": ""}, "inAnySourceOf": {"title": "In any source of", "description": ""}, "selectEventAttribute": {"title": "Select event attribute", "description": ""}, "selectAttribute": {"title": "Select attribute", "description": ""}, "selectAttributeCapitalize": {"title": "Select Attribute", "description": ""}, "fromBo": {"title": "From BO", "description": ""}, "fromCotentSources": {"title": "From Content sources", "description": ""}, "fromEvent": {"title": "From Event", "description": ""}, "selectBo": {"title": "Select BO", "description": ""}, "selectContentSources": {"title": "Select Content source", "description": ""}, "by": {"title": "by", "description": ""}, "in": {"title": "in", "description": ""}, "last": {"title": "last", "description": ""}, "to": {"title": "to", "description": ""}, "today": {"title": "Today", "description": ""}, "yesterday": {"title": "Yesterday", "description": ""}, "selectAnItem": {"title": "Select an item", "description": ""}, "inputYourValue": {"title": "Input your value", "description": ""}, "confirmDeleteContentSourceGroup": {"title": "Confirm delete Content source", "blocksDescription": "This Content source is being used in the following blocks:", "deleteDescription": "If you delete this Content source, the blocks in use will be deleted"}, "confirmChangeContentSource": {"title": "Confirm change Content source", "blocksDescription": "This Content source is being used in the following blocks:", "deleteDescription": "If you change this Content source, the blocks in use will be deleted"}, "slideShow": {"title": "Slide show", "description": "", "info": "<strong>Slide transition</strong>, <strong>Auto slide</strong>, <strong>Slide loop</strong> only works for Preview or Delivery"}, "rating": {"title": "Rating", "description": "", "ratingType": {"title": "Rating type", "options": {"star": "Star", "heart": "Heart"}}, "valueType": "Value type", "value": "Value", "colors": {"before": "BG before rating color", "after": "BG after rating color"}}, "ratingSize": {"title": "Rating size", "description": ""}, "maxRating": {"title": "Max rating", "description": ""}, "allowHalfRating": {"title": "Allow half rating", "description": ""}, "showRatingText": {"title": "Show rating text", "description": ""}, "ratingLabels": {"title": "Rating labels", "description": ""}, "removeAll": {"title": "Remove all"}, "totalItems": {"title": "Total items", "description": ""}, "displayItems": {"title": "Display items", "description": ""}, "slideTransition": {"title": "Slide transition", "description": ""}, "numberOfNextItem": {"title": "Number of next items", "description": ""}, "numberOfItems": {"title": "Number of items", "description": ""}, "numberOfRows": {"title": "Number Of Rows", "description": ""}, "numberOfColumns": {"title": "Number Of Columns", "description": ""}, "autoSlide": {"title": "Auto slide", "description": ""}, "slideDelay": {"title": "Slide delay (second)", "description": ""}, "slideTime": {"title": "Slide time (second)", "description": ""}, "slideLoop": {"title": "Slide loop", "description": ""}, "horizontal": {"title": "Horizontal", "description": ""}, "vertical": {"title": "Vertical", "description": ""}, "requireErrorMessage": {"title": "Required Error Message", "description": "", "placeholder": "The {{name}} field is required."}, "invalidMessage": {"title": "Invalid Message", "description": "", "placeholder": "Invalid {{name}} format"}, "invalidMessagePhone": {"title": "A valid phone number is required. Allowed characters: any numeric digit, (), -, +, . and length >= 10."}, "full": {"title": "Full", "description": ""}, "half": {"title": "Half", "description": ""}, "columnGapPx": {"title": "Column gap (px)", "description": ""}, "cellGapPx": {"title": "Cells Gap (px)", "description": ""}, "slide": {"title": "Slide", "description": ""}, "fade": {"title": "Fade", "description": ""}, "cube": {"title": "C<PERSON>", "description": ""}, "flip": {"title": "Flip", "description": ""}, "cards": {"title": "Cards", "description": ""}, "coverflow": {"title": "Coverflow", "description": ""}, "creative": {"title": "Creative", "description": ""}, "nextPreviousButton": {"title": "Next & Previous <PERSON><PERSON>", "description": ""}, "middle": {"title": "Middle", "description": ""}, "topCenter": {"title": "Top Center", "description": ""}, "bottomCenter": {"title": "Bottom Center", "description": ""}, "buttonIconColor": {"title": "Button Icon Color", "description": ""}, "buttonShape": {"title": "<PERSON>ton Sha<PERSON>", "description": ""}, "bgNormalColor": {"title": "BG normal color", "description": ""}, "bgNormalOpacity": {"title": "", "description": ""}, "bgHoverColor": {"title": "BG hover color", "description": ""}, "bgHoverOpacity": {"title": "BG hover opacity", "description": ""}, "slideStyling": {"title": "Slide Styling", "description": ""}, "customCode": {"title": "Custom code", "description": ""}, "selectSource": {"title": "Select Source", "label": "Source", "placeholder": "Select source"}, "thisFieldIsRequired": {"title": "This field is required", "description": ""}, "selectCollection": {"title": "Select collection", "description": ""}, "selectCategory": {"title": "Select category", "description": ""}, "selectAField": {"title": "Select a field", "description": ""}, "selectContent": {"title": "Select content", "description": ""}, "orSelectAField": {"title": "or Select a field", "description": ""}, "valueFilterEmpty": {"title": "Value filter empty", "description": ""}, "attrDisabled": {"title": "", "description": "Data-updating of this attribute has been disabled. You still can use the output value of the last time it is processed"}, "dynamicLink": {"button": {"insert": "Insert"}, "modal": {"error": {"boAttr": "Error getting Business Object Attributes", "customerAttr": "Error getting Customer Attributes", "eventAttr": "Error getting Event Attributes", "eventBySource": "Error getting Event in selected Source", "promotionCodeAttr": "Error getting Promotion Code Attributes", "promotionPools": "Error getting Promotion Pools", "source": "Error getting Sources", "visitorAttr": "Error getting Visitor Attributes"}, "label": {"index": "Index", "openNewTab": "Open in new tab", "contentSource": "Content Source", "attribute": "Attribute", "promotionCodeAttr": "Promotion code attribute", "promotionPools": "Promotion pools", "selectEvent": "Select event", "selectEventAttr": "Select event attribute", "selectSource": "In any source of", "text": "Text", "title": "Title", "url": "URL"}, "linkType": {"dynamic": "Dynamic", "static": "Static"}, "message": {"confirmDeleteLink": "Are you sure to delete this Dynamic Link ?", "selectDisableEventAttr": "Data-updating of this attribute has been disabled. You still can use the output value of the last time it is processed"}, "dynamicContentType": {"customerAttr": "Customer Attribute", "eventAttr": "Event Attribute", "promotionCode": "Promotion Code", "visitorAttr": "Visitor Attribute"}, "placeholder": {"enterValue": "Enter value..."}, "title": {"deleteLink": "Delete Dynamic Link", "insertLink": "Insert Link"}}}, "dynamicContent": {"title": "Dynamic content", "modal": {"error": {"boAttr": "Error getting Business Object Attributes", "customerAttr": "Error getting Customer Attributes", "eventAttr": "Error getting Event Attributes", "eventBySource": "Error getting Event in selected Source", "promotionCodeAttr": "Error getting Promotion Code Attributes", "promotionPools": "Error getting Promotion Pools", "source": "Error getting Sources", "visitorAttr": "Error getting Visitor Attributes", "perName": "This name already existed", "function": "Invalid function"}, "label": {"index": "Index", "contentSource": "Content Source", "personalizationName": "Personalization Name", "function": "Function", "ouputDataType": "Output Data Type", "ouputFormat": "Output Format", "saveTemplate": "Save as template", "attribute": "Attribute", "promotionCodeAttr": "Promotion code attribute", "promotionPools": "Promotion pools", "selectEvent": "Select event", "selectEventAttr": "Select event attribute", "selectSource": "In any source of", "displayFormat": "Display Format", "numberFormated": "Display Format", "formNumberDisplayFormat": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "currencySymbol": "Symbol", "currencyCode": "Code", "compact": "Compact numbers", "decimalPlace": "Decimal Places", "grouping": "Grouping", "decimal": "Decimal", "groupingIconTooltipTitle": "Select a mark to separate between groups of thounsands", "decimalIconTooltipTitle": "Select a mark to separate integer and its decimalds"}, "datimeDisplayFormat": {"dateDF": "Date display format", "timeDF": "Time display format", "short": "Short", "medium": "Medium", "long": "<PERSON>", "use24hour": "Use 24-hour clock"}}, "message": {"confirmDeleteVariable": "Are you sure to delete this Dynamic Text ?", "selectDisableEventAttr": "Data-updating of this attribute has been disabled. You still can use the output value of the last time it is processed"}, "dynamicContentType": {"customerAttr": "Customer Attribute", "eventAttr": "Event Attribute", "promotionCode": "Promotion Code", "visitorAttr": "Visitor Attribute", "custom": "Custom"}, "attrDisplayFormat": {"number": "Number", "percentage": "Percentage", "currency": "<PERSON><PERSON><PERSON><PERSON>", "datetime": "Datetime", "rawString": "Raw string"}, "title": {"addDynamicContent": "Add Dynamic Content", "deleteVariable": "Delete Dynamic Text"}}, "sidePanel": {"highlightCheckbox": "Highlight Dynamic Content", "title": "Dynamic Content", "addDynamicContent": "Add dynamic content"}}, "textStyling": {"sidePanel": {"title": "Text styling", "numberOfRows": {"title": "Number of Lines"}, "ellipsisText": {"title": "Ellipsis text"}}, "info": "<strong>Text styling</strong> only works for Preview or Delivery"}, "pleaseInputValue": {"title": "Please input value & press Enter", "description": ""}, "export": {"title": "Export"}, "search": {"title": "Search ..."}, "selectItems": {"title": "Select items"}, "showChecked": {"title": "Show checked"}, "viewAll": {"title": "View all"}, "uncheckAll": {"title": "Uncheck all"}, "listingPerformance": {"controls": {"columns": {"dropdown": {"savedColumns": "Your saved columns", "trigger": "Modify columns ..."}, "modal": {"add": {"content": {"addAllColumns": "Add all columns", "dragdrop": "Drag and drop to reorder", "removeAllColumns": "Remove all columns", "selectMetric": "Select metrics", "searchColumns": "Find modify columns"}, "footer": {"checkbox": "Save this set of columns", "input": {"placeholder": "Modify column name"}}, "title": "Modify Columns"}, "remove": {"message": "Are you sure want to remove ?", "title": "Remove Modify Column"}}}, "download": {"options": {"all": "Export all data", "selected": "Export selected data", "page": "Export data in current page"}, "title": "Export Data"}, "filter": {"dropdown": {"filterValue": {"placeholder": {"text": "Example ABC, 123"}}, "savedFilters": {"empty": "No saved filters", "title": "Saved Filters"}, "trigger": "Add filter"}, "modal": {"duplicatedFilterSet": {"message": "The filter {{filterName}} already exists. Would you like to write over it ?", "title": "Message"}, "removeFilterSet": {"message": "Are you sure to remove the filter {{filterName}} ?", "title": "Remove filter confirm"}, "saveFilterSet": {"label": "Saved filter name", "title": "New saved filter"}}}, "search": {"addFilter": "Filter on", "redirect": "Or go to"}}, "media-template": {"remove": {"message": "Are you sure to remove selected template(s) ?", "notification": {"failed": "Removed failed", "success": "Remove {{numRows}} Template(s) success"}, "title": "Remove {{numRows}} template(s)"}}, "media-json": {"remove": {"message": "Are you sure to remove selected template(s) ?", "notification": {"failed": "Removed failed", "success": "Remove {{numRows}} Template(s) success"}, "title": "Remove {{numRows}} template(s)"}}}, "fallback": {"title": "Fallback", "options": {"none": "None", "hidden": "Hidden", "mostSeen": "Most seen", "mostCart": "Most cart", "mostBought": "Most bought"}}, "noWrap": {"title": "No wrap", "description": ""}, "wrap": {"title": "Wrap", "description": ""}, "gap": {"title": "Gap", "description": ""}, "display": {"title": "Display", "description": ""}, "direction": {"title": "Direction", "description": ""}, "justify": {"title": "Justify", "description": ""}, "containerHoverStyling": {"warning": "<strong>Warning:</strong> Changing Position, Spacing value is too high from default value sometimes causes the browser not work properly."}, "errorBoundary": {"button": "Refresh", "message": "Sorry.. there was an error, please refresh page again. <br /> Or we'll automatically refresh page in ({{second}}s)"}, "addMore": {"title": "Add more", "description": ""}, "keywords": {"title": "Keywords", "description": ""}, "sort": {"title": "Sort", "description": ""}, "order": {"title": "Order", "description": ""}, "mix": {"title": "Mix", "description": ""}, "title": {"title": "Title", "description": ""}, "tags": {"title": "Tags", "description": ""}, "products": {"title": "Products", "description": ""}, "articles": {"title": "Articles", "description": ""}, "setValues": {"title": "Set values", "description": ""}, "countDownStyle": {"title": "Countdown Style", "options": {"grouping": "Grouping", "separate": "Separate"}}, "separator": {"title": "Separator", "options": {"colon": "Colon", "hyphen": "Hyphen", "slash": "Slash"}}, "separatorStyling": {"title": "Separator <PERSON>", "description": ""}, "tableBlock": {"title": "Table", "description": ""}, "shakeAndWin": {"title": "Shake and win", "description": ""}, "shakeStyling": {"title": "<PERSON>", "description": ""}, "defaultReminderNotification": {"title": "Default Reminder Notification", "description": " "}, "reminderNotificationStyling": {"title": "Reminder Notification Styling", "description": " "}, "reminderNotificationLabel": {"title": "<PERSON><PERSON><PERSON> ngay để nhận quà", "description": ""}, "surpriseTreasureHunt": {"title": "Surprise Treasure Hunt", "description": ""}, "luckyGiftBox": {"title": "Lucky Gift Box", "description": ""}, "giftBox": {"title": "Gift box", "description": ""}, "giftCells": {"title": "Cells", "description": ""}, "giftBoxStyling": {"title": "Gift Box Styling", "description": ""}, "highlight": {"title": "Highlight", "description": ""}, "shake": {"title": "Shake", "description": ""}, "shakeTriggerSetting": {"title": "<PERSON>gger Setting", "description": ""}, "shakeTrigger": {"title": "<PERSON>", "description": ""}, "imageBeforeShake": {"title": "Image Before Shake", "titleUrl": "Image Before Shake URL", "description": ""}, "imageToShake": {"title": "Image To Shake", "titleUrl": "Image To Shake URL", "description": ""}, "imageAfterShake": {"title": "Image After Shake", "titleUrl": "Image After Shake URL", "description": ""}, "system": {"title": "System", "description": ""}, "userAction": {"title": "User Action", "description": ""}, "shakeBy": {"title": "By", "description": ""}, "referral": {"title": "Referral", "description": ""}, "clickOnThisBlock": {"title": "Click on This block", "description": ""}, "optinField": {"title": "Optin field", "description": ""}, "horizontalShake": {"title": "Horizontal Shake", "description": ""}, "jumpShake": {"title": "Jump & Shake", "description": ""}, "horizontalSkewedShaking": {"title": "Horizontal Skewed Shaking", "description": ""}, "verticalSkewedShaking": {"title": "Vertical Skewed Shaking", "description": ""}, "constantTilt": {"title": "Constant Tilt", "description": ""}, "verticalShake": {"title": "Vertical Shake", "description": ""}, "timeToShake": {"title": "Time To Shake", "description": ""}, "timeToDelay": {"title": "Time To Delay", "description": ""}, "translationTime": {"title": "Transition time", "description": ""}, "customizeRewards": {"title": "Customize Rewards", "description": ""}, "reminderNotification": {"title": "Remider Notification", "description": "", "placeholder": "Remider Notification"}, "shakeType": {"title": "Shake Type", "description": ""}, "dimensions": {"title": "Dimensions", "description": ""}, "addDimension": {"title": "Add dimension", "description": ""}, "metric": {"title": "Metric", "description": ""}, "addMetric": {"title": "Add metric", "description": ""}, "showTop": {"title": "Show top", "description": ""}, "tableStyling": {"title": "Table styling", "description": ""}, "showHeader": {"title": "Show header", "description": ""}, "wrapText": {"title": "Wrap text", "description": ""}, "tableHeader": {"title": "Table header", "description": ""}, "tableColor": {"title": "Table color", "description": ""}, "tableHeaderBackground": {"title": "Header background color", "description": ""}, "tableCellBorder": {"title": "Cell border color", "description": ""}, "tableOddRowColor": {"title": "Odd row color", "description": ""}, "tableEvenRowColor": {"title": "Even row color", "description": ""}, "tableLabels": {"title": "Table labels", "description": ""}, "tableBody": {"title": "Table body", "description": ""}, "tableFooter": {"title": "Table footer", "description": ""}, "rowNumbers": {"title": "Row numbers", "description": ""}, "missingData": {"title": "Missing data", "description": ""}, "dimensionStyling": {"title": "Dimension styling", "description": ""}, "metricStyling": {"title": "Metric styling", "description": ""}, "number": {"title": "Number", "description": ""}, "heatMap": {"title": "Heatmap", "description": ""}, "bar": {"title": "Bar", "description": ""}, "columnStyle": {"title": "Column style", "description": ""}, "noDimensionSelected": {"title": "Don't have dimension selected", "description": ""}, "noMetricSelected": {"title": "Don't have metric selected", "description": ""}, "showNull": {"title": "Show \"null\"", "description": ""}, "showZero": {"title": "Show \"0\"", "description": ""}, "showNoData": {"title": "Show \"No data\"", "description": ""}, "showBlank": {"title": "Show \"\" (blank)"}, "layout": {"title": "Layout", "description": ""}, "responsiveDesign": {"title": "Responsive Design", "hideOnMobile": "Hide on Mobile", "hideOnDesktop": "Hide on Desktop", "notStackOnMobile": "Do Not Stack on Mobile"}, "descending": {"title": "Descending", "description": ""}, "ascending": {"title": "Ascending", "description": ""}, "saveLongWarning": {"title": "This process takes a long time to process", "description": ""}, "saveTemplate": {"success": "Save Template Success", "failed": "Save Template Failed"}, "interest": {"title": "interest", "description": ""}, "setIndex": {"title": "Set index", "indexFrom": "Index from", "setAutoIndex": "Set auto index", "warningTooltips": "The index of the blocks in the {{name}} is not the same", "setIndexSuccess": {"title": "Set Index Success", "description": "{{childBlockName}} in the {{blockName}} will have index from {{fromIndex}} to {{toIndex}}"}, "maxIndex": "Max index up to {{number}}"}, "thumbnailCapture": {"title": "Thumbnail capture", "tooltip": "Turning off thumbnail capture makes saving process faster"}, "object": {"title": "Object", "description": ""}, "array": {"title": "Array", "description": ""}, "boolean": {"title": "Boolean", "description": ""}, "keyCode": {"title": "Key code", "placeholder": "Enter key code here..."}, "fieldKeyCode": {"title": "Field Key code", "placeholder": "Enter field key code here..."}, "value": {"title": "Value", "placeholder": "Enter value here..."}, "addingBlocks": {"title": "Adding blocks", "description": "Let's start by adding blocks"}, "ordinalText": {"first": "First", "second": "Second", "third": "Third", "fourth": "Fourth", "fifth": "Fifth", "sixth": "Sixth", "seventh": "Seventh", "eighth": "Eighth", "ninth": "Ninth", "tenth": "Tenth"}, "addColor": {"title": "Add color", "description": ""}, "duplicatedKey": {"title": "Key is duplicated in object", "description": ""}, "verifyFormat": {"title": "Verify format", "description": ""}, "dynamicFields": {"title": "Dynamic Fields", "description": ""}, "validJsonFormat": {"title": "Json format is valid"}, "name": {"title": "Name", "description": ""}, "autoClose": {"title": "Auto close", "description": ""}, "at": {"title": "At", "description": ""}, "after": {"title": "After", "description": ""}, "verifiedSubmit": {"title": "Verified submit", "description": ""}, "attribute": {"title": "Attribute", "description": ""}, "where": {"title": "Where", "description": ""}, "refineByAttribute": {"title": "Refine by attribute", "description": ""}, "fieldNotExist": {"title": "This field doesn't exist", "description": ""}, "trackingClick": {"title": "Tracking Click", "description": ""}, "syncDataToBO": {"title": "Sync data to Business Object", "description": ""}, "templateName": {"title": "Template Name"}, "alternativeText": {"title": "Alternative Text"}, "richMenuSettings": {"title": "<PERSON>"}, "advancedImageMapSettings": {"title": "Advanced Imagemap", "menuImage": "Image"}, "areaAction": {"title": "Area Action"}, "richMenuDetails": {"title": "<PERSON>", "menuName": "<PERSON>u Name", "aliasID": "Alias ID", "setAtDefault": "Set as default Rich <PERSON>u"}, "areaLayout": {"title": "Area Layout", "customizedLayout": "Customized layout", "numOfColumns": "Number of columns", "numOfRows": "Number of rows", "maxAreaError": "The number of area must not higher than {{max}}"}, "menuImage": {"title": "Menu Image", "uploadMode": "Upload mode"}, "chatBar": {"title": "Chat Bar", "menuBarLabel": "Menu Bar Label", "displayChatBarByDefault": "Display chat bar by default"}, "action": {"title": "Action", "actionType": "Action Type", "uriType": "URI Type", "phoneNumber": "Phone Number"}, "single": {"title": "Single"}, "multiple": {"title": "Multiple"}, "addNewMenu": {"title": "Add New Menu"}, "actionType": {"none": {"title": "No Action"}, "message": {"title": "Message Action"}, "messageText": {"title": "Message Text"}, "uri": {"title": "URI Action"}, "redirectUrl": {"title": "Redirect to an URL"}, "video": {"title": "Play a video"}, "richMenuSwitch": {"title": "Switch <PERSON>"}, "postBack": {"title": "Postback Action"}, "dateTimePicker": {"title": "Datetime Picker"}}, "uriType": {"url": {"title": "Redirect to an URL"}, "tel": {"title": "Call a telephone number"}}, "formatType": {"date": {"title": "Date"}, "time": {"title": "Time"}, "dateTime": {"title": "Datetime"}}, "cellAction": {"title": "Click to add image"}, "templateListing": {"emptyDescription": "No template available", "blankTemplate": "Blank template", "editTemplate": "Edit template", "useTemplate": "Use template", "newTemplate": "New template", "createCampaign": "Create Campaign"}, "noProvider": {"label": {"title": "No service provider available"}, "content": {"title": "Currently, there are no available service provider in your portal. Please contact our support team for further assistance"}}, "switchMode": {"title": "Switch mode", "content": "Switching mode will discard all your current design. Are you sure you want to continue?", "description": ""}, "noSupportMobile": {"title": "This display type is not supported on mobile apps and will only appear on websites.", "description": ""}, "displayArea": {"title": "Display Area", "description": ""}, "safeArea": {"title": "Safe Area", "description": ""}, "fullBleed": {"title": "Full Bleed", "description": ""}, "warnDisplayArea": {"title": "Only applicable for mobile apps", "description": ""}}