// Libraries
import React, { memo } from 'react';

// Types
import { BlockProps } from 'app/modules/Dashboard/containers/MediaJson/containers/Design/types';

// Atoms
import { ObjectKey } from '../../atoms/ObjectKey';
import { ObjectValue } from '../../atoms/ObjectValue';

// Styled
import { VideoBlockWrapper } from './styled';

interface VideoBlockProps extends BlockProps {}

export const VideoBlock: React.FC<VideoBlockProps> = memo(props => {
  // Props
  const { settings, type } = props;

  // Variables
  const { key, value } = settings || {};

  return (
    <VideoBlockWrapper className="variable-row">
      <ObjectKey objectKey={key} objectType={type} />
      <ObjectValue value={value} />
    </VideoBlockWrapper>
  );
});
