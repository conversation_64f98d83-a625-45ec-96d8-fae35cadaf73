// Locales
import { initialsErrorMessage, random } from 'app/utils/common';
import { DROPDOWN_DATE, DROPDOWN_TIME, LIST_DROPDOWN_SELECT } from 'app/components/molecules/DragBlock/constant';
import { translations } from 'locales/translations';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Images
import imageText from 'assets/images/icons/text.png';
import areaText from 'assets/images/icons/textarea.png';
import numberImage from 'assets/images/icons/number.png';
import radioImage from 'assets/images/icons/radio.png';
import checkboxImage from 'assets/images/icons/checkbox.png';
import dropdownSelectImage from 'assets/images/icons/dropdown-select.png';
import dateTimeImage from 'assets/images/icons/datetime.png';
import { RATING_OPTIONS_BY_TYPE, RATING_TYPE } from 'app/components/atoms/Rating/constants';

export const FIELDS_SETTING = {
  nameInput: {
    icon: 'icon-ants-person',
    name: getTranslateMessage(translations.optinFields.fields.nameField.title),
  },
  firstNameInput: {
    icon: 'icon-ants-badge',
    name: getTranslateMessage(translations.optinFields.fields.firstNameField.title),
  },
  lastNameInput: {
    icon: 'icon-ants-badge',
    name: getTranslateMessage(translations.optinFields.fields.lastNameField.title),
  },
  emailInput: {
    icon: 'icon-ants-email',
    name: getTranslateMessage(translations.optinFields.fields.emailField.title),
  },
  phoneInput: {
    icon: 'icon-ants-phone-android',
    name: getTranslateMessage(translations.optinFields.fields.phoneField.title),
  },
  privacyText: {
    icon: 'icon-ants-privacy-tip',
    name: getTranslateMessage(translations.optinFields.fields.privacyNotice.title),
  },
  dropdownSelect: {
    icon: '',
    image: dropdownSelectImage,
    name: getTranslateMessage(translations.optinFields.fields.dropdownSelect.title),
  },
  datetime: {
    icon: '',
    image: dateTimeImage,
    name: getTranslateMessage(translations.optinFields.fields.datetime.title),
  },
  textInput: {
    icon: '',
    image: imageText,
    name: getTranslateMessage(translations.optinFields.fields.inputField.title),
  },
  textArea: {
    icon: '',
    image: areaText,
    name: getTranslateMessage(translations.optinFields.fields.inputAreaField.title),
  },
  number: {
    icon: '',
    image: numberImage,
    name: getTranslateMessage(translations.optinFields.fields.numberField.title),
  },
  radioButton: {
    icon: '',
    image: radioImage,
    name: getTranslateMessage(translations.optinFields.fields.radioButton.title),
  },
  checkbox: {
    icon: '',
    image: checkboxImage,
    name: getTranslateMessage(translations.optinFields.fields.checkbox.title),
  },
  rating: {
    icon: '',
    image: checkboxImage,
    name: getTranslateMessage(translations.optinFields.fields.rating.title),
  },
  submitButton: {
    name: getTranslateMessage(translations.submitButton.title),
  },
};

export const CUSTOM_FIELD = {
  textInput: {
    id: 'textInput',
    inputName: 'textInput',
    name: getTranslateMessage(translations.optinFields.fields.inputField.title),
    type: 'textInput',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.inputField.placeholder),
    required: true,
    isCustom: true,
    inputWidth: '80%',
    fieldWidth: '100%',
    label: getTranslateMessage(translations.optinFields.fields.inputField.title),
    displayLabel: true,
    alignField: 'horizontal',
    nameField: getTranslateMessage(translations.optinFields.fields.inputField.title),
    requireErrorMessage: '',
    errorText: initialsErrorMessage('text'),
    invalidText: '',
    isShowField: true,
  },
  textArea: {
    id: 'textArea',
    inputName: 'textArea',
    name: getTranslateMessage(translations.optinFields.fields.inputAreaField.title),
    type: 'textArea',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.inputAreaField.placeholder),
    required: true,
    isCustom: true,
    inputWidth: '80%',
    fieldWidth: '100%',
    label: getTranslateMessage(translations.optinFields.fields.inputAreaField.title),
    displayLabel: true,
    alignField: 'horizontal',
    nameField: getTranslateMessage(translations.optinFields.fields.inputAreaField.title),
    requireErrorMessage: '',
    errorText: initialsErrorMessage('text area'),
    invalidText: '',
    isShowField: true,
  },
  number: {
    id: 'number',
    inputName: 'number',
    name: getTranslateMessage(translations.optinFields.fields.numberField.title),
    type: 'number',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.numberField.placeholder),
    required: true,
    isCustom: true,
    inputWidth: '80%',
    fieldWidth: '100%',
    label: getTranslateMessage(translations.optinFields.fields.numberField.title),
    displayLabel: true,
    alignField: 'horizontal',
    nameField: getTranslateMessage(translations.optinFields.fields.numberField.title),
    requireErrorMessage: '',
    invalidText: 'Invalid number format',
    errorText: initialsErrorMessage('number'),
    isShowField: true,
    isRangeNumber: true,
    minValue: '',
    maxValue: '',
  },
  radioButton: {
    id: 'radioButton',
    inputName: 'radioButton',
    name: getTranslateMessage(translations.optinFields.fields.radioButton.title),
    type: 'radioButton',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.radioButton.placeholder),
    required: true,
    isCustom: true,
    inputWidth: '80%',
    fieldWidth: '100%',
    label: getTranslateMessage(translations.optinFields.fields.radioButton.title),
    displayLabel: true,
    alignField: 'horizontal',
    nameField: getTranslateMessage(translations.optinFields.fields.radioButton.title),
    requireErrorMessage: '',
    errorText: initialsErrorMessage('radio button'),
    invalidText: '',
    isShowField: true,
    optionColumn: '1',
    fieldOptions: [
      {
        value: '1',
        label: 'Option 1',
        isPreselect: true,
        goToView: 'none',
        order: 0,
        id: random(5),
      },
    ],
  },
  checkbox: {
    id: 'checkbox',
    inputName: 'checkbox',
    name: getTranslateMessage(translations.optinFields.fields.checkbox.title),
    type: 'checkbox',
    order: -1,
    required: true,
    isCustom: true,
    inputWidth: '80%',
    fieldWidth: '100%',
    label: getTranslateMessage(translations.optinFields.fields.checkbox.title),
    displayLabel: true,
    alignField: 'horizontal',
    nameField: getTranslateMessage(translations.optinFields.fields.checkbox.title),
    requireErrorMessage: '',
    errorText: initialsErrorMessage('checkbox'),
    invalidText: '',
    isShowField: true,
    optionColumn: '1',
    fieldOptions: [
      {
        value: '1',
        label: 'Option 1',
        isPreselect: true,
        order: 0,
        id: random(5),
      },
    ],
  },
  dropdownSelect: {
    id: 'dropdownSelect',
    inputName: 'dropdownSelect',
    name: getTranslateMessage(translations.optinFields.fields.dropdownSelect.title),
    type: 'dropdownSelect',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.dropdownSelect.placeholder),
    label: getTranslateMessage(translations.optinFields.fields.dropdownSelect.title),
    inputWidth: '80%',
    fieldWidth: '100%',
    alignField: 'horizontal',
    nameField: getTranslateMessage(translations.optinFields.fields.dropdownSelect.title),
    required: false,
    isCustom: true,
    errorText: initialsErrorMessage('dropdown select'),
    listDropdownSelect: LIST_DROPDOWN_SELECT,
    displayLabel: true,
    isShowField: true,
  },
  datetime: {
    id: 'datetime',
    inputName: 'datetime',
    name: getTranslateMessage(translations.optinFields.fields.datetime.title),
    nameField: getTranslateMessage(translations.optinFields.fields.datetime.title),
    label: getTranslateMessage(translations.optinFields.fields.datetime.title),
    alignField: 'horizontal',
    type: 'datetime',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.datetime.placeholder),
    inputWidth: '80%',
    fieldWidth: '100%',
    required: true,
    isCustom: true,
    datetimeType: 'datetime',
    dateTimeFormat: 'dd/MM/yyyy',
    dateTimeFormatLabel: 'DD/MM/YYYY',
    errorText: initialsErrorMessage('datetime'),
    timeFormat: 'am_pm',
    dropdownDate: DROPDOWN_DATE,
    dropdownTime: DROPDOWN_TIME,
    displayLabel: true,
    isShowField: true,
  },
  rating: {
    id: 'rating',
    inputName: 'rating',
    name: getTranslateMessage(translations.optinFields.fields.rating.title),
    nameField: getTranslateMessage(translations.optinFields.fields.rating.name),
    label: getTranslateMessage(translations.optinFields.fields.rating.label),
    alignField: 'horizontal',
    type: 'rating',
    order: -1,
    placeholder: getTranslateMessage(translations.optinFields.fields.rating.placeholder),
    required: true,
    isCustom: true,
    inputWidth: '80%',
    fieldWidth: '100%',
    displayLabel: true,
    requireErrorMessage: '',
    errorText: initialsErrorMessage('rating'),
    invalidText: '',
    isShowField: true,
    ratingType: RATING_TYPE.STAR, // star, smiley, yesno
    ratingOptions: RATING_OPTIONS_BY_TYPE[RATING_TYPE.STAR].map(option => ({
      ...option,
      id: random(6),
    })),
    alignment: 'left', // left, center, right
    size: 30, // px
    optionColumn: 5,
    borderColor: '#F7DA64',
    bgBeforeColor: '#FFFFFF',
    bgAfterColor: '#F7DA64',
  },
};

export const CUSTOM_FIELD_WIDTH = {
  dropdownSelect: {
    type: 'dropdownSelect',
    width: 370,
  },
  radioButton: {
    type: 'radioButton',
    width: 370,
  },
  datetime: {
    type: 'datetime',
    width: 250,
  },
  checkbox: {
    type: 'checkbox',
    width: 250,
  },
  number: {
    type: 'number',
    width: 250,
  },
  textArea: {
    type: 'textArea',
    width: 250,
  },
  textInput: {
    type: 'textInput',
    width: 250,
  },
  rating: {
    type: 'rating',
    width: 300,
  },
};
