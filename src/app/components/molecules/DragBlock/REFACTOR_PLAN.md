# Kế hoạch tái cấu trúc (Refactor Plan) cho Component `DragBlock`

## 1. <PERSON><PERSON><PERSON> tiêu

Tái cấu trúc lại logic render nội dung trong popover của component `DragBlock` để cải thiện cấu trúc code, gi<PERSON><PERSON> dễ đọc, d<PERSON> bảo trì và dễ mở rộng hơn trong tương lai.

**Quan trọng:** Việc tái cấu trúc này chỉ tập trung vào việc tổ chức lại code, **không làm thay đổi bất kỳ logic hay giao diện (UI) nào** đang có.

## 2. Phân tích hiện trạng

Logic render nội dung cho popover hiện tại nằm hoàn toàn trong file `src/app/components/molecules/DragBlock/index.tsx`, cụ thể là trong biến `content` (bắt đầu từ dòng 357). <PERSON>hối JSX này rất lớn, chứa nhiều logic lồng nhau và các điều kiện phức tạp, g<PERSON><PERSON> kh<PERSON> kh<PERSON>n cho việc bảo trì.

## 3. Kế hoạch chi tiết

Chúng ta sẽ tách khối JSX phức tạp trên thành các component con, mỗi component chịu trách nhiệm cho một phần cụ thể của UI. Các component này sẽ được đặt trong một thư mục mới: `src/app/components/molecules/DragBlock/components/PopoverContent/`.

### 3.1. Cấu trúc Component mới

Sơ đồ sau minh họa cấu trúc component được đề xuất:

```mermaid
graph TD
    A[DragBlock] --> B{Popover};
    B --> C[PopoverContent];

    subgraph PopoverContent
        C --> D[GeneralSettings];
        C --> E[LayoutSettings];
        C --> F{FieldTypeSpecificSettings};
        C --> G[AdvancedSettings];
    end

    subgraph FieldTypeSpecificSettings
        F --> F1[DateTimeSettings];
        F --> F2[OptionBasedSettings];
        F --> F3[DropdownSettings];
    end

    subgraph AdvancedSettings
        G --> G1[ValidationSettings];
        G --> G2[NumberInputSettings];
        G --> G3[PhoneInputSettings];
        G --> G4[PrivacyTextSettings];
    end

    style PopoverContent fill:#f9f,stroke:#333,stroke-width:2px
    style FieldTypeSpecificSettings fill:#ccf,stroke:#333,stroke-width:2px
    style AdvancedSettings fill:#cfc,stroke:#333,stroke-width:2px
```

### 3.2. Nhiệm vụ của các Component

- **`PopoverContent/index.tsx`**: Component chính, đóng vai trò "nhạc trưởng". Nó sẽ nhận `blockSetting` và các `handler` từ `DragBlock`, sau đó quyết định render các component con nào dựa trên `blockSetting.type` và `blockSetting.id`.

- **`GeneralSettings.tsx`**: Render các cài đặt chung: `Field Name`, `Field Label`, `Display Label`, `Placeholder`.

- **`LayoutSettings.tsx`**: Render các cài đặt về bố cục: `Field Alignment`, `Field Width`, `Input Width`.

- **`FieldTypeSpecificSettings/` (Thư mục)**:

  - **`DateTimeSettings.tsx`**: Chứa logic render cho `type: 'datetime'`.
  - **`OptionBasedSettings.tsx`**: Chứa logic render cho `type: 'radioButton'` và `type: 'checkbox'`.
  - **`DropdownSettings.tsx`**: Chứa logic render cho `type: 'dropdownSelect'`.

- **`AdvancedSettings/` (Thư mục)**:
  - **`ValidationSettings.tsx`**: Component tái sử dụng cho logic `Required`, `Error Message`, `Invalid Message`.
  - **`NumberInputSettings.tsx`**: Logic cho `type: 'number'`.
  - **`PhoneInputSettings.tsx`**: Logic cho `id: 'phoneInput'`.
  - **`PrivacyTextSettings.tsx`**: Logic cho `id: 'privacyText'`.

## 4. Đảm bảo tính toàn vẹn

- **Luồng dữ liệu (Data Flow)**: State (`blockSetting`) và các hàm xử lý (`onChangeDragFieldSetting`, `setBlockSetting`) vẫn được quản lý tại component `DragBlock` gốc và được truyền xuống các component con thông qua `props`.
- **Giao diện (UI)**: Việc "cắt" và "dán" các khối JSX sẽ được thực hiện cẩn thận để đảm bảo không có sự thay đổi nào về giao diện người dùng.

## 5. Các bước thực hiện

1.  Tạo file `REFACTOR_PLAN.md` (đã hoàn thành).
2.  Chuyển sang **Code Mode** để bắt đầu implement.
3.  Tạo các thư mục và file component như trong kế hoạch.
4.  Di chuyển từng phần logic từ `DragBlock/index.tsx` sang các component con tương ứng.
5.  Thay thế khối `content` trong `DragBlock/index.tsx` bằng component `PopoverContent` mới.
6.  Kiểm tra và đảm bảo mọi thứ hoạt động như cũ.
