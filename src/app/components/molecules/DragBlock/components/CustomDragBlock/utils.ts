import { random } from 'app/utils/common';
import { Tfield } from '../DragOption';

export const formatStringToOptions = (str: string, type: string | undefined) => {
  try {
    const lines = str.split('\n');
    const results: Tfield[] = [];
    lines
      .map(l => l.trim())
      .filter(Boolean)
      .forEach((line, index) => {
        const values = line.split(', ');
        if ((type && type === 'radioButton') || type === 'dropdownSelect') {
          results.push({
            order: index,
            id: random(5),
            value: values[0],
            label: values[1],
            isPreselect: Boolean(values[2] === 'true'),
            goToView: values[3],
          });
        } else {
          results.push({
            order: index,
            id: random(5),
            value: values[0],
            label: values[1],
            isPreselect: Boolean(values[2] === 'true'),
          });
        }
      });
    return results;
  } catch (error) {}
};

export const OBJECT_FIELDS_NUMBER = {
  radioButton: 4,
  checkbox: 3,
  dropdownSelect: 4,
};

export const validateExtendValue = (str: string, type: string) => {
  try {
    const lines = str.split('\n');
    let result = false;
    lines
      .map(l => l.trim())
      .filter(Boolean)
      .forEach(line => {
        const values = line.split(', ');
        if (values.length !== OBJECT_FIELDS_NUMBER[type] || (values[2] !== 'true' && values[2] !== 'false')) {
          result = true;
        }
      });
    return result;
  } catch (error) {
    return true;
  }
};
