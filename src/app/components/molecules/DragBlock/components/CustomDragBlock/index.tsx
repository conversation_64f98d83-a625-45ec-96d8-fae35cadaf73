// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { cloneDeep } from 'lodash';
import { DragDropContext, DropResult, Droppable } from 'react-beautiful-dnd';
import { translations } from 'locales/translations';
import { useTranslation } from 'react-i18next';

// Atoms
import { Button, Popover, Text, TextArea } from 'app/components/atoms';

// Utils
import { random, reorder } from 'app/utils/common';
import { formatStringToOptions, validateExtendValue } from './utils';

// Styled
import { Wrapper<PERSON>uttonFooter, WrapperFooter, WrapperHeader } from './styled';

// Components
import DragOption, { Tfield } from '../DragOption';
import { THEME } from '@antscorp/antsomi-ui/es/constants';

type TFieldArrays = {
  id: string;
  type: string;
  fieldOptions?: Tfield[];
  listDropdownSelect?: Tfield[];
};

type CustomDragBlockProps = {
  value: Tfield[];
  id: string;
  titles: string[];
  type: string;
  idField?: string;
  fieldsArray?: TFieldArrays[];
  onChange: (value: Tfield[]) => void;
};

const CustomDragBlock: React.FC<CustomDragBlockProps> = props => {
  const { id, value, titles, onChange, type, idField, fieldsArray } = props;

  // State
  const [visiblePopoverExtends, setVisiblePopoverExtends] = useState(false);
  const [nextNumOptions, setNextNumOptions] = useState(1);
  const [valueExtends, setValueExtends] = useState('');

  let listData = useMemo(() => {
    const dataClone = cloneDeep(value);

    return dataClone;
  }, [value]);

  // I18n
  const { t } = useTranslation();

  // Actions
  const isDisplayHeader = ['dropdownSelect', 'checkbox', 'radioButton'].includes(type);
  const isDisplayFooter = ['dropdownSelect', 'checkbox', 'radioButton'].includes(type);

  const errorFormatExtendValue = useMemo(() => {
    try {
      if (visiblePopoverExtends) {
        return validateExtendValue(valueExtends, type);
      }
    } catch (error) {
      return false;
    }
  }, [type, valueExtends, visiblePopoverExtends]);

  const memoIdViewPage = useMemo(() => {
    let result = '';

    (fieldsArray || []).forEach(field => {
      if (field && (field.type === 'radioButton' || field.type === 'dropdownSelect')) {
        const objectKey = {
          dropdownSelect: 'listDropdownSelect',
          radioButton: 'fieldOptions',
        } as const;

        const label = objectKey[field.type];

        const fieldOptions = field[label];

        if (fieldOptions && !!fieldOptions.length) {
          const itemGoToView = fieldOptions.find(item => item.goToView && item.goToView !== 'none');

          if (itemGoToView) {
            result = field.id;
          }
        }
      }
    });

    return result;
  }, [fieldsArray]);

  useEffect(() => {
    let result = '';

    if (listData && visiblePopoverExtends) {
      let filterArray: Tfield[] = [];

      if (type === 'radioButton' || type === 'dropdownSelect') {
        filterArray = listData.map(item => ({
          value: item.value,
          label: item.label,
          isPreselect: item.isPreselect,
          goToView: item.goToView,
        }));
      } else {
        filterArray = listData.map(item => ({
          value: item.value,
          label: item.label,
          isPreselect: item.isPreselect,
        }));
      }

      filterArray.forEach(field => {
        result += Object.values(field).join(', ') + '\n';
      });

      setValueExtends(result);
    }
  }, [listData, type, visiblePopoverExtends]);

  const handleUpdateDropdown = (data: Tfield[]) => {
    onChange(data);
  };

  const handleApplyExtendValue = () => {
    const newOptionFields = formatStringToOptions(valueExtends, type);

    if (newOptionFields) {
      onChange([...newOptionFields]);
    }

    setVisiblePopoverExtends(false);
  };

  const handleExtendsValue = () => {
    setVisiblePopoverExtends(true);
  };

  const content = (
    <div className="ants-h-[260px] ants-w-[484px]">
      <Text style={{ color: '#000' }} className="ants-mt-[5px]">
        {t(translations.extendValues.title)}
      </Text>

      <TextArea
        value={valueExtends}
        placeholder={t(translations.extendValues.placeholder)}
        rows={10}
        onChange={e => {
          setValueExtends(e.target.value);
        }}
      />

      <WrapperButtonFooter>
        <Button
          type="primary"
          className="ants-font-normal"
          onClick={handleApplyExtendValue}
          disabled={!!errorFormatExtendValue}
        >
          {t(translations.apply.title)}
        </Button>

        <Button
          type="text"
          style={{ border: '1px solid #b8cfe6' }}
          className="ants-font-normal"
          onClick={() => setVisiblePopoverExtends(false)}
        >
          {t(translations.cancel.title)}
        </Button>
      </WrapperButtonFooter>
    </div>
  );

  const onDragEnd = (result: DropResult) => {
    // dropped outside the list || dropped itself
    if (!result.destination || result.destination.index === result.source.index) {
      return;
    }

    const reorderFields = reorder(value, result.source?.index, result.destination?.index)
      // change order based on index of new array
      .map((item, index) => ({
        ...item,
        order: index,
      }));

    onChange(reorderFields);
  };

  const onAddAnOption = () => {
    setNextNumOptions(nextNumOptions + 1);

    const option: Tfield = {
      order: nextNumOptions,
      id: random(6),
      value: nextNumOptions + 1,
      label: `Option ${nextNumOptions + 1}`,
      isPreselect: false,
      goToView: 'none',
    };

    listData.push(option);
    onChange(listData);
  };

  const onRemoveAll = () => {
    onChange([]);
  };

  return (
    <div className="ants-flex ants-flex-col">
      <div style={{ width: '150px', display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
        {titles.map((title, index) => (
          <Text key={`${title}-${index}`}>{title}</Text>
        ))}
      </div>

      {isDisplayHeader && (
        <WrapperHeader>
          <div className="value-header">Value</div>
          <div className="label-header">Label</div>
          <div className="preselect-header">Preselect</div>

          {type !== 'checkbox' && <div className="go-to-view-header">Go to view</div>}

          <Button className="remove-all-header" color="#005fb8" type="link" onClick={onRemoveAll}>
            Remove all
          </Button>
        </WrapperHeader>
      )}

      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId={id}>
          {provided => (
            <div ref={provided.innerRef} {...provided.droppableProps}>
              {value.map((field, index) => {
                return (
                  <DragOption
                    fields={value}
                    idOption={field.id}
                    index={index}
                    idField={idField}
                    key={field.id}
                    type={type}
                    settings={field}
                    onChange={onChange}
                    handleUpdateDropdown={handleUpdateDropdown}
                    memoIdViewPage={memoIdViewPage}
                  />
                );
              })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {isDisplayFooter && (
        <WrapperFooter>
          <Button color={THEME.token?.colorPrimary} type="link" onClick={onAddAnOption}>
            + {t(translations.addAnOption.title)}
          </Button>

          <Popover
            placement="bottomRight"
            trigger={['click']}
            content={content}
            visible={visiblePopoverExtends}
            destroyTooltipOnHide
          >
            <Button color={THEME.token?.colorPrimary} type="link" onClick={handleExtendsValue}>
              {t(translations.extendValues.title)}
            </Button>
          </Popover>
        </WrapperFooter>
      )}
    </div>
  );
};

export default CustomDragBlock;
