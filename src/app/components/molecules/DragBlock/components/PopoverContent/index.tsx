// Libraries
import React, { useMemo } from 'react';
import classNames from 'classnames';

// Components
import { GeneralSettings } from './GeneralSettings';
import { LayoutSettings } from './LayoutSettings';
import { DropdownSettings } from './FieldTypeSpecificSettings/DropdownSettings';
import { DateTimeSettings } from './FieldTypeSpecificSettings/DateTimeSettings';
import { OptionBasedSettings } from './FieldTypeSpecificSettings/OptionBasedSettings';
import { RatingSettings } from './FieldTypeSpecificSettings/RatingSettings';
import { AdvancedSettings } from './AdvancedSettings';
import { CUSTOM_FIELD_WIDTH } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/organisms/FormFieldsSetting/constants';

interface PopoverContentProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  optinFields: any;
  fieldsArray: any[];
  settings: any;
  handleOnchangeRange: (value: any, type: string) => void;
}

export const PopoverContent: React.FC<PopoverContentProps> = props => {
  const {
    blockSetting,
    setBlockSetting,
    onChangeDragFieldSetting,
    optinFields,
    fieldsArray,
    settings,
    handleOnchangeRange,
  } = props;

  const widthPopup = useMemo(() => {
    let width: number | null = null;

    if (blockSetting?.isCustom) {
      const configType = Object.values(CUSTOM_FIELD_WIDTH).find(field => field.type === blockSetting.type);
      if (configType) {
        width = configType.width;
      }
    }
    return width;
  }, [blockSetting?.isCustom, blockSetting.type]);

  return (
    <div className={classNames(`ants-flex ants-flex-col ants-space-y-5 ${widthPopup ? 'ants-w-100' : 'ants-w-72'}`)}>
      <GeneralSettings
        blockSetting={blockSetting}
        setBlockSetting={setBlockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        optinFields={optinFields}
      />

      <DropdownSettings
        blockSetting={blockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        settings={settings}
        fieldsArray={fieldsArray}
      />

      <DateTimeSettings
        blockSetting={blockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        settings={settings}
        fieldsArray={fieldsArray}
      />

      <OptionBasedSettings
        blockSetting={blockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        settings={settings}
        fieldsArray={fieldsArray}
      />

      <RatingSettings
        blockSetting={blockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        settings={settings}
        fieldsArray={fieldsArray}
      />

      <LayoutSettings
        blockSetting={blockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        settings={settings}
      />

      <AdvancedSettings
        blockSetting={blockSetting}
        setBlockSetting={setBlockSetting}
        onChangeDragFieldSetting={onChangeDragFieldSetting}
        settings={settings}
        handleOnchangeRange={handleOnchangeRange}
      />
    </div>
  );
};
