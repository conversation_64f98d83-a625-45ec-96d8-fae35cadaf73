// Libraries
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Text, TextArea } from 'app/components/atoms';

// Molecules
import { SwitchLabel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/SwitchLabel';

interface PrivacyTextSettingsProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  settings: any;
}

export const PrivacyTextSettings: React.FC<PrivacyTextSettingsProps> = props => {
  const { blockSetting, setBlockSetting, onChangeDragFieldSetting, settings } = props;
  const { t } = useTranslation();

  return (
    <Fragment>
      {blockSetting.id === 'privacyText' && (
        <Fragment>
          <div className="ants-flex ants-flex-col ants-mt-4">
            <SwitchLabel
              label={t(translations.privacyCheckbox.title)}
              checked={blockSetting.showCheckbox}
              onChange={checked => onChangeDragFieldSetting('showCheckbox', checked)}
            />
          </div>
          <div className="ants-mt-4">
            <SwitchLabel
              label={t(translations.required.title)}
              checked={blockSetting?.required}
              onChange={checked => onChangeDragFieldSetting('required', checked)}
            />
          </div>
          <div className="ants-flex ants-flex-col ants-mt-4">
            <Text className="ants-mb-5px">{t(translations.errorMessage.title)}</Text>
            <TextArea
              showBorder
              value={blockSetting?.errorText}
              status={blockSetting?.required && !blockSetting?.errorText ? 'error' : ''}
              placeholder={t(translations.errorMessage.description)}
              rows={3}
              onChange={e => setBlockSetting({ ...blockSetting, errorText: e.target.value })}
            />
            {blockSetting?.required && !blockSetting?.errorText && (
              <span style={{ marginTop: 5, marginLeft: '0.5rem', fontSize: '12px', color: 'rgb(255, 77, 79)' }}>
                {t(translations.requireErrorMessage.placeholder, {
                  name: settings && settings?.name ? settings?.name.toLowerCase() : '',
                })}
              </span>
            )}
          </div>
        </Fragment>
      )}
    </Fragment>
  );
};
