// Libraries
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Text, TextArea } from 'app/components/atoms';

// Molecules
import { SwitchLabel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/SwitchLabel';
import { InputNumber } from 'app/components/molecules/InputNumber';

interface NumberInputSettingsProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  handleOnchangeRange: (value: any, type: string) => void;
}

export const NumberInputSettings: React.FC<NumberInputSettingsProps> = props => {
  const { blockSetting, setBlockSetting, onChangeDragFieldSetting, handleOnchangeRange } = props;
  const { t } = useTranslation();

  return (
    <Fragment>
      {blockSetting.type === 'number' && (
        <Fragment>
          <div className="ants-mt-4">
            <SwitchLabel
              label={t(translations.rangeNumber.title)}
              checked={blockSetting?.isRangeNumber}
              onChange={checked => onChangeDragFieldSetting('isRangeNumber', checked)}
            />
          </div>
          <div className="ants-flex ants-justify-between ants-mt-4">
            <Text>{t(translations.minValue.title)}</Text>
            <div className="ants-w-[57px]">
              <InputNumber
                value={blockSetting?.minValue || ''}
                onChange={value => handleOnchangeRange(value, 'minValue')}
              />
            </div>
          </div>
          <div className="ants-flex ants-justify-between ants-mt-4">
            <Text>{t(translations.maxValue.title)}</Text>
            <div className="ants-w-[57px]">
              <InputNumber
                value={blockSetting?.maxValue || ''}
                onChange={value => handleOnchangeRange(value, 'maxValue')}
              />
            </div>
          </div>
          <div className="ants-flex ants-flex-col ants-mt-4">
            <Text className="ants-mb-5px">{t(translations.invalidMessage.title)}</Text>
            <TextArea
              showBorder
              value={blockSetting?.invalidText}
              placeholder={t(translations.invalidMessage.placeholder, {
                name: blockSetting?.name,
              })}
              rows={3}
              onChange={e => setBlockSetting({ ...blockSetting, invalidText: e.target.value })}
            />
          </div>
        </Fragment>
      )}
    </Fragment>
  );
};
