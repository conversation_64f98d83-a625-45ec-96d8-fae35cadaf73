// Libraries
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Molecules
import { SwitchLabel } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/components/organisms/SidePanel/components/molecules/SwitchLabel';
import { Select } from '../../../../Select';

// Constants
import { COUNTRY_OPTIONS } from '../../../constant';
import { TextArea } from 'app/components/atoms';

interface PhoneInputSettingsProps {
  blockSetting: any;
  setBlockSetting: (setting: any) => void;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  settings: any;
}

export const PhoneInputSettings: React.FC<PhoneInputSettingsProps> = props => {
  const { blockSetting, setBlockSetting, onChangeDragFieldSetting, settings } = props;
  const { t } = useTranslation();

  return (
    <Fragment>
      {blockSetting.id === 'phoneInput' && (
        <Fragment>
          <div className="ants-mt-4">
            <SwitchLabel
              label={t(translations.enablePhoneValidation.title)}
              checked={blockSetting.phoneValidationEnabled}
              onChange={checked => onChangeDragFieldSetting('phoneValidationEnabled', checked)}
            />
          </div>
          <div className="ants-mt-4">
            <SwitchLabel
              label={t(translations.useCountryCode.title)}
              checked={blockSetting.useCountryCode}
              onChange={checked => onChangeDragFieldSetting('useCountryCode', checked)}
            />
          </div>
          {blockSetting.useCountryCode && (
            <div className="ants-mt-4">
              <Select
                showSearch
                label={t(translations.countryCodeDefault.title)}
                value={blockSetting.countryCodeDefault ? blockSetting.countryCodeDefault.value : ''}
                options={Object.values(COUNTRY_OPTIONS)}
                onChange={value => {
                  onChangeDragFieldSetting('countryCodeDefault', {
                    value: COUNTRY_OPTIONS[value] && COUNTRY_OPTIONS[value].value,
                    code: COUNTRY_OPTIONS[value] && COUNTRY_OPTIONS[value].code,
                    isoCode: COUNTRY_OPTIONS[value] && COUNTRY_OPTIONS[value].isoCode,
                  });
                }}
              />
            </div>
          )}
          <div className="ants-flex ants-flex-col ants-mt-4">
            <span className="ants-mb-5px">{t(translations.invalidMessage.title)}</span>
            <TextArea
              showBorder
              value={blockSetting?.invalidText}
              placeholder={t(translations.invalidMessage.placeholder, {
                name: settings?.name,
              })}
              rows={3}
              onChange={e => setBlockSetting({ ...blockSetting, invalidText: e.target.value })}
            />
          </div>
        </Fragment>
      )}
    </Fragment>
  );
};
