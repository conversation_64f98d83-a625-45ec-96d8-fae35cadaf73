// Libraries
import React from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

// Components
import { Text, Button, Icon } from 'app/components/atoms';

// Utils
import { getTranslateMessage } from 'utils/messages';
import { random } from 'app/utils/common';

// Translations
import { translations } from 'locales/translations';

// Styled
import styled from 'styled-components';
import { Select } from '@antscorp/antsomi-ui';

const RatingOptionsWrapper = styled.div`
  .rating-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .rating-option-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-bottom: 8px;
    background: white;

    &:hover {
      border-color: #1890ff;
    }
  }

  .drag-handle {
    cursor: grab;
    color: #999;

    &:active {
      cursor: grabbing;
    }
  }

  .option-value {
    width: 40px;
    text-align: center;
    font-weight: 500;
  }

  .option-label-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 4px 8px;
    border-radius: 4px;

    &:focus {
      background: #f0f8ff;
    }
  }

  .preselect-star {
    cursor: pointer;
    font-size: 16px;
    color: #d9d9d9;
    transition: color 0.2s;

    &.selected {
      color: #faad14;
    }

    &:hover {
      color: #faad14;
    }
  }

  .goto-view-select {
    min-width: 80px;
  }

  .remove-button {
    color: #ff4d4f;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background: #fff2f0;
    }
  }

  .add-option-button {
    width: 100%;
    margin-top: 8px;
    border: 1px dashed #d9d9d9;
    background: transparent;
    color: #1890ff;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }
  }
`;

interface RatingOption {
  id: string;
  value: number;
  label: string;
  isPreselect: boolean;
  goToView: string;
}

interface RatingOptionsManagerProps {
  options: RatingOption[];
  onChange: (options: RatingOption[]) => void;
  fieldsArray?: any[];
}

export const RatingOptionsManager: React.FC<RatingOptionsManagerProps> = props => {
  const { options = [], onChange, fieldsArray = [] } = props;

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(options);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update values based on new order
    const updatedItems = items.map((item, index) => ({
      ...item,
      value: index + 1,
    }));

    onChange(updatedItems);
  };

  const handleLabelChange = (optionId: string, newLabel: string) => {
    const updatedOptions = options.map(option => (option.id === optionId ? { ...option, label: newLabel } : option));
    onChange(updatedOptions);
  };

  const handlePreselectChange = (optionId: string) => {
    const updatedOptions = options.map(option => ({
      ...option,
      isPreselect: option.id === optionId,
    }));
    onChange(updatedOptions);
  };

  const handleGoToViewChange = (optionId: string, goToView: string) => {
    const updatedOptions = options.map(option => (option.id === optionId ? { ...option, goToView } : option));
    onChange(updatedOptions);
  };

  const handleRemoveOption = (optionId: string) => {
    const filteredOptions = options.filter(option => option.id !== optionId);
    // Reorder values
    const reorderedOptions = filteredOptions.map((option, index) => ({
      ...option,
      value: index + 1,
    }));
    onChange(reorderedOptions);
  };

  const handleAddOption = () => {
    const newOption: RatingOption = {
      id: random(6),
      value: options.length + 1,
      label: `Option ${options.length + 1}`,
      isPreselect: false,
      goToView: 'none',
    };
    onChange([...options, newOption]);
  };

  // Create view options for dropdown
  const viewOptions = [
    { value: 'none', label: 'None' },
    ...fieldsArray.map(field => ({
      value: field.id,
      label: field.name || field.id,
    })),
  ];

  return (
    <RatingOptionsWrapper>
      <div className="rating-options-header">
        <Text>{getTranslateMessage(translations.fieldOptions.title)}</Text>
        <Button
          type="link"
          onClick={() => {
            /* Handle remove all */
          }}
          style={{ color: '#ff4d4f', fontSize: '12px' }}
        >
          {getTranslateMessage(translations.removeAll.title)}
        </Button>
      </div>

      <div style={{ display: 'flex', gap: '8px', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
        <div style={{ width: '24px' }}></div>
        <div style={{ width: '40px', textAlign: 'center' }}>Value</div>
        <div style={{ flex: 1 }}>Label</div>
        <div style={{ width: '24px', textAlign: 'center' }}>Preselect</div>
        <div style={{ width: '80px' }}>Go to view</div>
        <div style={{ width: '24px' }}></div>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="rating-options">
          {provided => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {options.map((option, index) => (
                <Draggable key={option.id} draggableId={option.id} index={index}>
                  {provided => (
                    <div ref={provided.innerRef} {...provided.draggableProps} className="rating-option-item">
                      <div {...provided.dragHandleProps} className="drag-handle">
                        <Icon type="icon-ants-material-outline-drag-indicator" />
                      </div>

                      <div className="option-value">{option.value}</div>

                      <input
                        type="text"
                        value={option.label}
                        onChange={e => handleLabelChange(option.id, e.target.value)}
                        className="option-label-input"
                        placeholder="Enter label"
                      />

                      <div
                        className={`preselect-star ${option.isPreselect ? 'selected' : ''}`}
                        onClick={() => handlePreselectChange(option.id)}
                      >
                        ★
                      </div>

                      <Select
                        className="goto-view-select"
                        options={viewOptions}
                        value={option.goToView}
                        onSelect={value => handleGoToViewChange(option.id, value)}
                      />

                      <div className="remove-button" onClick={() => handleRemoveOption(option.id)}>
                        <Icon type="icon-ants-material-outline-close" />
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <Button
        className="add-option-button"
        onClick={handleAddOption}
        icon={<Icon type="icon-ants-material-outline-add" />}
      >
        + Add an option
      </Button>
    </RatingOptionsWrapper>
  );
};
