// Libraries
import React from 'react';
import { Text, Select, InputNumber, Slider, Icon } from '@antscorp/antsomi-ui';

// Components
import { RatingTypeSelector } from './RatingTypeSelector';
import { RatingOptionsManager } from './RatingOptionsManager';

// Constants
import { RATING_ALIGNMENT, BORDER_STYLE } from '../../../constant';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Translations
import { translations } from 'locales/translations';

interface RatingSettingsProps {
  blockSetting: any;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  settings: any;
  fieldsArray?: any[];
}

export const RatingSettings: React.FC<RatingSettingsProps> = props => {
  const { blockSetting, onChangeDragFieldSetting, settings, fieldsArray = [] } = props;

  // Only show for rating fields
  if (!blockSetting.isCustom || blockSetting.type !== 'rating') {
    return null;
  }

  return (
    <>
      {/* Rating Type Selector */}
      <RatingTypeSelector
        value={settings?.ratingType || 'star'}
        onChange={value => onChangeDragFieldSetting('ratingType', value)}
      />

      {/* Field Options */}
      <RatingOptionsManager
        options={settings?.ratingOptions || []}
        onChange={options => onChangeDragFieldSetting('ratingOptions', options)}
        fieldsArray={fieldsArray}
      />

      {/* Alignment */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.align.title)}</Text>
        <div className="ants-flex ants-gap-2">
          {Object.values(RATING_ALIGNMENT).map(align => (
            <button
              key={align.value}
              className={`ants-px-3 ants-py-1 ants-border ants-rounded ${
                settings?.alignment === align.value ? 'ants-border-blue-500 ants-bg-blue-50' : 'ants-border-gray-300'
              }`}
              onClick={() => onChangeDragFieldSetting('alignment', align.value)}
            >
              <Icon type={`icon-ants-material-outline-format-align-${align.value}`} />
            </button>
          ))}
        </div>
      </div>

      {/* Size */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.size.title)} (px)</Text>
        <div className="ants-flex ants-items-center ants-gap-2 ants-flex-1 ants-max-w-48">
          <Slider
            min={10}
            max={100}
            value={settings?.size || 30}
            onChange={value => onChangeDragFieldSetting('size', value)}
            className="ants-flex-1"
          />
          <InputNumber
            min={10}
            max={100}
            value={settings?.size || 30}
            onChange={value => onChangeDragFieldSetting('size', value)}
            className="ants-w-16"
          />
        </div>
      </div>

      {/* Option Column */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.optionColumn.title)}</Text>
        <Select
          options={[
            { value: 1, label: '1' },
            { value: 2, label: '2' },
            { value: 3, label: '3' },
            { value: 4, label: '4' },
            { value: 5, label: '5' },
          ]}
          value={settings?.optionColumn || 5}
          onChange={value => onChangeDragFieldSetting('optionColumn', value)}
          className="ants-w-20"
        />
      </div>

      {/* Border Style */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.borderStyle.title)}</Text>
        <Select
          options={Object.values(BORDER_STYLE)}
          value={settings?.borderStyle || 'solid'}
          onChange={value => onChangeDragFieldSetting('borderStyle', value)}
          className="ants-w-24"
        />
      </div>

      {/* Border Color */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.borderColor.title)}</Text>
        <div className="ants-flex ants-items-center ants-gap-2">
          <input
            type="color"
            value={settings?.borderColor || '#F7DA64'}
            onChange={e => onChangeDragFieldSetting('borderColor', e.target.value)}
            className="ants-w-8 ants-h-8 ants-border ants-rounded"
          />
          <Text className="ants-text-xs ants-text-gray-500">{settings?.borderColor || '#F7DA64'}</Text>
        </div>
      </div>

      {/* BG Before Rating Color */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.rating.colors.before)}</Text>
        <div className="ants-flex ants-items-center ants-gap-2">
          <input
            type="color"
            value={settings?.bgBeforeColor || '#FFFFFF'}
            onChange={e => onChangeDragFieldSetting('bgBeforeColor', e.target.value)}
            className="ants-w-8 ants-h-8 ants-border ants-rounded"
          />
          <Text className="ants-text-xs ants-text-gray-500">{settings?.bgBeforeColor || '#FFFFFF'}</Text>
        </div>
      </div>

      {/* BG After Rating Color */}
      <div className="ants-flex ants-justify-between ants-items-center">
        <Text>{getTranslateMessage(translations.rating.colors.after)}</Text>
        <div className="ants-flex ants-items-center ants-gap-2">
          <input
            type="color"
            value={settings?.bgAfterColor || '#F7DA64'}
            onChange={e => onChangeDragFieldSetting('bgAfterColor', e.target.value)}
            className="ants-w-8 ants-h-8 ants-border ants-rounded"
          />
          <Text className="ants-text-xs ants-text-gray-500">{settings?.bgAfterColor || '#F7DA64'}</Text>
        </div>
      </div>
    </>
  );
};
