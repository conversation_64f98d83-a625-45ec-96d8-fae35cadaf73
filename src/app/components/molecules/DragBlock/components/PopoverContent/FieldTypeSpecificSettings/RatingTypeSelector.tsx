// Libraries
import React from 'react';
import classNames from 'classnames';

// Components
import { Text } from 'app/components/atoms';

// Constants
import { RATING_TYPE } from '../../../constant';

// Utils
import { getTranslateMessage } from 'utils/messages';

// Translations
import { translations } from 'locales/translations';

// Styled
import styled from 'styled-components';

const RatingTypeSelectorWrapper = styled.div`
  .rating-type-options {
    display: flex;
    gap: 8px;
    margin-top: 8px;
  }

  .rating-type-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 16px;
    border: 2px solid #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    min-width: 80px;

    &:hover {
      border-color: #1890ff;
    }

    &.selected {
      border-color: #1890ff;
      background: #f0f8ff;
    }

    .rating-type-icon {
      font-size: 24px;
      margin-bottom: 4px;
    }

    .rating-type-label {
      font-size: 12px;
      color: #666;
      text-align: center;
    }

    &.selected .rating-type-label {
      color: #1890ff;
      font-weight: 500;
    }
  }
`;

interface RatingTypeSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

export const RatingTypeSelector: React.FC<RatingTypeSelectorProps> = props => {
  const { value, onChange } = props;

  const handleSelect = (ratingType: string) => {
    onChange(ratingType);
  };

  return (
    <RatingTypeSelectorWrapper>
      <Text>{getTranslateMessage(translations.rating.ratingType.title)}</Text>
      <div className="rating-type-options">
        {Object.values(RATING_TYPE).map(type => (
          <div
            key={type.value}
            className={classNames('rating-type-option', {
              selected: value === type.value,
            })}
            onClick={() => handleSelect(type.value)}
          >
            <div className="rating-type-icon">{type.icon}</div>
            <div className="rating-type-label">{type.label}</div>
          </div>
        ))}
      </div>
    </RatingTypeSelectorWrapper>
  );
};
