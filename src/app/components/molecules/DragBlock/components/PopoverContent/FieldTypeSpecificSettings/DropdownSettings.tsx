// Libraries
import React from 'react';

// Components
import CustomDragBlock from '../../../components/CustomDragBlock';

interface DropdownSettingsProps {
  blockSetting: any;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  settings: any;
  fieldsArray: any[];
}

export const DropdownSettings: React.FC<DropdownSettingsProps> = props => {
  const { blockSetting, onChangeDragFieldSetting, settings, fieldsArray } = props;

  return (
    <>
      {blockSetting.isCustom && blockSetting.type === 'dropdownSelect' && (
        <CustomDragBlock
          id={`${blockSetting?.id}-dropdownSelect`}
          idField={blockSetting?.id}
          type={blockSetting.type}
          titles={['Field Options']}
          value={settings?.listDropdownSelect}
          fieldsArray={fieldsArray}
          onChange={value => onChangeDragFieldSetting('listDropdownSelect', value)}
        />
      )}
    </>
  );
};
