// Libraries
import React from 'react';
import { useTranslation } from 'react-i18next';

// Translations
import { translations } from 'locales/translations';

// Atoms
import { Divider } from 'app/components/atoms';

// Molecules
import { Select } from '../../../Select';
import { SliderWithInputNumber } from 'app/components/molecules/SliderWithInputNumber';
import { SettingWrapper } from 'app/modules/Dashboard/components/common/SettingWrapper';

// Utils
import { getNumberFromString } from 'app/utils/common';

// Constants
import { ALIGNMENT_OPTIONS, FIELD_CUSTOM_OPTIONS, FIELD_OPTIONS } from '../../constant';

interface LayoutSettingsProps {
  blockSetting: any;
  onChangeDragFieldSetting: (key: string, value: any) => void;
  settings: any;
}

export const LayoutSettings: React.FC<LayoutSettingsProps> = props => {
  const { blockSetting, onChangeDragFieldSetting, settings } = props;
  const { t } = useTranslation();

  const inputWidth = getNumberFromString(`${blockSetting?.inputWidth}` || '');
  const fieldWidth = getNumberFromString(`${blockSetting?.fieldWidth}` || '');

  return (
    <>
      {(FIELD_OPTIONS.includes(blockSetting.id) ||
        blockSetting.id === 'privacyText' ||
        blockSetting.id === 'phoneInput' ||
        FIELD_CUSTOM_OPTIONS.includes(blockSetting.type)) && (
        <SettingWrapper label={t(translations.fieldAlignment.title)}>
          <Select
            className="ants-w-[112px]"
            options={Object.values(ALIGNMENT_OPTIONS)}
            value={settings?.alignField}
            onChange={value => onChangeDragFieldSetting('alignField', value)}
          />
        </SettingWrapper>
      )}

      {/* case unit === '%' */}
      <SliderWithInputNumber
        label={t(translations.fieldWidthPercent.title)}
        min={0}
        max={100}
        value={fieldWidth}
        onAfterChange={value => onChangeDragFieldSetting('fieldWidth', value)}
      />

      {settings?.alignField && settings.alignField === 'horizontal' && (
        <SliderWithInputNumber
          label={t(translations.inputWidthPercent.title)}
          min={0}
          max={100}
          value={inputWidth}
          onAfterChange={value => onChangeDragFieldSetting('inputWidth', value)}
        />
      )}
      <Divider dot />
    </>
  );
};
