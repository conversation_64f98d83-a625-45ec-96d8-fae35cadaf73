// Libraries
import React, { useEffect, useState } from 'react';
import { Draggable, DraggableProvided } from 'react-beautiful-dnd';
import produce from 'immer';
import { useDispatch, useSelector } from 'react-redux';

// Atoms
import { Checkbox, Icon, Input, Radio, Text } from 'app/components/atoms';

// Styles
import styles from '../CustomDragBlock/styles.module.scss';
import { DragBlockWrapper } from './styled';

// Molecules
import { Select } from 'app/components/molecules/Select';

// Selectors
import {
  selectSidePanel,
  selectViewPageSelected,
  selectViewPages,
} from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice/selectors';

// Hooks
import { useDebounce, useUpdateEffect } from 'app/hooks';

// Actions
import { mediaTemplateDesignActions } from 'app/modules/Dashboard/containers/MediaTemplate/containers/Design/slice';

// Utils
import { getObjSafely } from 'app/utils/common';

// Constant
import { ERROR_MESSAGES } from '../../constant';

export type Tfield = {
  id?: string;
  value?: string | number;
  label?: string;
  isPreselect?: boolean;
  goToView?: string;
  displayAlias?: string;
  order?: number;
  isActive?: boolean;
};

type TDropOptionProps = {
  type?: string;
  onChange: (value: Tfield[]) => void;
  idOption?: string;
  idField?: string;
  settings: Tfield;
  fields: Tfield[];
  index: number;
  memoIdViewPage?: string;
  handleUpdateDropdown?: (data: Tfield[]) => void;
};

const DragOption: React.FC<TDropOptionProps> = props => {
  const { index, type: typeField, settings, fields, idOption, handleUpdateDropdown, memoIdViewPage, idField } = props;
  const [blockSetting, setBlockSetting] = useState(settings || {});

  const debounceValue = useDebounce(blockSetting.value, 300);
  const debounceLabel = useDebounce(blockSetting.label, 300);
  const debounceDisplayAlias = useDebounce(blockSetting.displayAlias, 300);

  // Actions
  const { setSidePanelSettings } = mediaTemplateDesignActions;

  // Dispatch
  const dispatch = useDispatch();

  // Selectors
  const viewPages = useSelector(selectViewPages);
  const sidePanel = useSelector(selectSidePanel);
  const viewPageSelected = useSelector(selectViewPageSelected);
  const optinFields = getObjSafely(() => sidePanel.settings.optinFields);

  useEffect(() => {
    setBlockSetting(settings);
  }, [settings]);

  useUpdateEffect(() => {
    onChangeSetting('value', debounceValue);
    if (!blockSetting.value) {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              value: { ...optinFields.errors.value, [idOption || '']: ERROR_MESSAGES.IS_REQUIRE.label },
            },
          },
        }),
      );
    } else {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              value: { ...optinFields.errors.value, [idOption || '']: '' },
            },
          },
        }),
      );
    }
  }, [debounceValue]);

  useUpdateEffect(() => {
    onChangeSetting('label', debounceLabel);
    if (!blockSetting.label) {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              label: { ...optinFields.errors.label, [idOption || '']: ERROR_MESSAGES.IS_REQUIRE.label },
            },
          },
        }),
      );
    } else {
      dispatch(
        setSidePanelSettings({
          optinFields: {
            errors: {
              ...optinFields.errors,
              label: { ...optinFields.errors.label, [idOption || '']: '' },
            },
          },
        }),
      );
    }
  }, [debounceLabel]);

  useUpdateEffect(() => {
    onChangeSetting('displayAlias', debounceDisplayAlias);
  }, [debounceDisplayAlias]);

  const handleClickRadio = (id: string) => {
    if (fields && !!fields.length) {
      if (handleUpdateDropdown) {
        const afterField = produce(fields, afterFields => {
          const fieldChanged = afterFields.find(field => field.id === id);
          if (fieldChanged && fieldChanged.id) {
            if (!fieldChanged.isPreselect) {
              return;
            }
            fieldChanged.isPreselect = false;
          }
        });
        handleUpdateDropdown([...afterField]);
      }
    }
  };

  const onDeleteOption = (id: string) => {
    const newListData = fields.filter(data => data.id !== id);
    if (handleUpdateDropdown) {
      handleUpdateDropdown([...newListData]);
    }
  };

  const onChangeSetting = (type: string, value: boolean) => {
    if (handleUpdateDropdown) {
      if (fields && !!fields.length) {
        if (type === 'isPreselect' && (typeField === 'radioButton' || typeField === 'dropdownSelect')) {
          const afterField = produce(fields, afterFields => {
            return afterFields.map(field => {
              if (field.id === idOption) {
                return {
                  ...field,
                  [type]: value,
                };
              }
              return { ...field, [type]: false };
            });
          });
          handleUpdateDropdown([...afterField]);
        } else {
          const afterField = produce(fields, afterFields => {
            const fieldChanged = afterFields.find(field => field.id === idOption);
            if (fieldChanged && fieldChanged.id) {
              fieldChanged[type] = value;
            }
          });
          handleUpdateDropdown([...afterField]);
        }
      }
    }
  };

  const renderDragBlock = (provided: DraggableProvided, field: Tfield): React.ReactElement => {
    let element: React.ReactNode = null;

    switch (typeField) {
      case 'datetime': {
        element = (
          <>
            <div className="left-block-datetime">
              <div className="wrapper-title">
                <Icon type="icon-ants-double-three-dots" size={16} className="icon" />

                <Text size={18} className="ants-flex-grow block-title">
                  {field.label}
                </Text>
              </div>

              <Input
                value={field.displayAlias}
                disabled={!field.isActive}
                placeholder={field.label}
                onChange={e => setBlockSetting({ ...blockSetting, displayAlias: e.target.value })}
                maxLength={255}
                style={{ height: '22px' }}
              />
            </div>

            <div className="right-block-datetime">
              <Checkbox checked={field.isActive} onChange={e => onChangeSetting('isActive', e.target.checked)} />
            </div>
          </>
        );
        break;
      }
      case 'dropdownSelect':
      case 'radioButton':
      case 'rating': {
        const listGoToView = [{ title: 'None', id: 'none' }, ...viewPages]
          .filter(each => each.id !== viewPageSelected)
          .map(page => {
            return {
              label: page.title,
              value: page.id,
            };
          });

        element = (
          <>
            <div className="left-block-dropdown-select">
              <Icon type="icon-ants-double-three-dots" size={16} className="icon" />

              <Input //index
                className={styles['value-input']}
                value={field.value}
                width={30}
                onChange={e => setBlockSetting({ ...blockSetting, value: e.target.value })}
              />

              <Input //Label
                className={styles['label-input']}
                value={field.label}
                width={60}
                debounce={500}
                onChange={e => setBlockSetting({ ...blockSetting, label: e.target.value })}
              />

              <Radio
                className={styles['radio-dropdown']}
                checked={field.isPreselect}
                onChange={e => onChangeSetting('isPreselect', e.target.checked)}
                onClick={() => {
                  if (settings.id) {
                    handleClickRadio(settings.id);
                  }
                }}
              />

              <Select
                className="ants-w-[82px]"
                options={listGoToView}
                value={field.goToView}
                onChange={value => onChangeSetting('goToView', value)}
                disabled={Boolean(memoIdViewPage && idField && memoIdViewPage !== idField)}
              />
            </div>

            <div className="right-block-dropdown">
              <Icon
                customClassName="icon-close"
                type="icon-ants-remove-slim"
                onClick={() => {
                  if (field.id) {
                    onDeleteOption(field.id);
                  }
                }}
              />
            </div>
          </>
        );
        break;
      }
      case 'checkbox': {
        element = (
          <>
            <div className="left-block-dropdown-select">
              <Icon type="icon-ants-double-three-dots" size={16} className="icon" />

              <Input
                className={styles['value-input']}
                value={field.value}
                width={30}
                onChange={e => setBlockSetting({ ...blockSetting, value: e.target.value })}
              />

              <Input
                className={styles['label-input']}
                value={field.label}
                style={{ width: 75 }}
                onChange={e => setBlockSetting({ ...blockSetting, label: e.target.value })}
              />

              <Checkbox
                checked={field.isPreselect}
                style={{ marginRight: 25 }}
                onChange={e => onChangeSetting('isPreselect', e.target.checked)}
              />
            </div>

            <div className="right-block-dropdown">
              <Icon
                customClassName="icon-close"
                type="icon-ants-remove-slim"
                onClick={() => {
                  if (field.id) {
                    onDeleteOption(field.id);
                  }
                }}
              />
            </div>
          </>
        );
        break;
      }
      default:
        break;
    }

    return (
      <DragBlockWrapper ref={provided?.innerRef} {...provided?.draggableProps} {...provided?.dragHandleProps}>
        <div className="ants-flex ants-flex-col ants-w-100">
          <div className="ants-flex ants-items-center">{element}</div>

          {(optinFields.errors.label || optinFields.errors.value) && (
            <div className="ants-mt-[2px]" style={{ color: '#ff4d4f', fontSize: 12 }}>
              {idOption
                ? getObjSafely(() => optinFields.errors.label[idOption]) ||
                  getObjSafely(() => optinFields.errors.value[idOption])
                : ''}
            </div>
          )}
        </div>
      </DragBlockWrapper>
    );
  };

  if (!idOption) {
    return null;
  }

  return (
    <Draggable key={idOption} draggableId={idOption} index={index}>
      {provide => renderDragBlock(provide, settings)}
    </Draggable>
  );
};

export default DragOption;
