// Translations
import { translations } from 'locales/translations';

// Utils
import { getTranslateMessage } from 'utils/messages';
import { formatCodeCountry } from './utils';
import { random } from 'app/utils/common';

export const FIELD_OPTIONS = ['lastNameInput', 'firstNameInput', 'nameInput', 'emailInput'];
export const FIELD_CUSTOM_OPTIONS = [
  'textInput',
  'textArea',
  'number',
  'radioButton',
  'checkbox',
  'datetime',
  'dropdownSelect',
  'rating',
];

export const ALIGNMENT_OPTIONS = {
  HORIZONTAL: {
    value: 'horizontal',
    label: getTranslateMessage(translations.horizontal.title),
  },
  VERTICAL: {
    value: 'vertical',
    label: getTranslateMessage(translations.vertical.title),
  },
};

export const DISPLAY_TYPE_DATE_TIME = {
  DATE_TIME: {
    value: 'datetime',
    label: 'Calendar',
  },
  DROP_DOWN: {
    value: 'dropdown',
    label: 'Dropdown',
  },
};

export const DATE_FORMAT = {
  YYYY: {
    value: 'yyyy',
    label: 'YYYY',
  },
  MM_YYYY: {
    value: 'MM/yyyy',
    label: 'MM/YYYY',
  },
  DD_MM_YYYY: {
    value: 'dd/MM/yyyy',
    label: 'DD/MM/YYYY',
  },
  MM_DD_YYYY: {
    value: 'MM/dd/yyyy',
    label: 'MM/DD/YYYY',
  },
  MM_DD: {
    value: 'MMM/dd',
    label: 'MMM/DD',
  },
  MM_DD_YYYY_HH_MM: {
    value: 'MM/dd/yyyy HH:mm',
    label: 'MM/DD/YYYY HH:mm',
  },
  MM_DD_YYYY_HH_mm_ss: {
    value: 'MM/dd/yyyy HH:mm:ss',
    label: 'MM/DD/YYYY HH:mm:SS',
  },
};

export const TIME_FORMAT = {
  AM_PM: {
    value: 'am_pm',
    label: 'AM/PM',
  },
  H24: {
    value: '24h',
    label: '24 hours',
  },
};

export const RATING_TYPE = {
  STAR: {
    value: 'star',
    label: 'Star',
    icon: '★',
  },
  SMILEY: {
    value: 'smiley',
    label: 'Smiley',
    icon: '😊',
  },
  YESNO: {
    value: 'yesno',
    label: 'Yes-No',
    icon: '👍',
  },
};

export const RATING_SIZE = {
  SMALL: {
    value: 'small',
    label: 'Small',
  },
  MEDIUM: {
    value: 'medium',
    label: 'Medium',
  },
  LARGE: {
    value: 'large',
    label: 'Large',
  },
};

export const RATING_ALIGNMENT = {
  LEFT: {
    value: 'left',
    label: 'Left',
  },
  CENTER: {
    value: 'center',
    label: 'Center',
  },
  RIGHT: {
    value: 'right',
    label: 'Right',
  },
};

export const BORDER_STYLE = {
  SOLID: {
    value: 'solid',
    label: 'Solid',
  },
  DASHED: {
    value: 'dashed',
    label: 'Dashed',
  },
  DOTTED: {
    value: 'dotted',
    label: 'Dotted',
  },
  NONE: {
    value: 'none',
    label: 'None',
  },
};

export const DROPDOWN_DATE = [
  {
    order: 0,
    id: 'day',
    label: 'Day',
    displayAlias: '',
    format: 'DD',
    isActive: true,
  },
  {
    order: 1,
    id: 'month',
    label: 'Month',
    displayAlias: '',
    format: 'MM',
    isActive: true,
  },
  {
    order: 2,
    id: 'year',
    label: 'Year',
    displayAlias: '',
    format: 'YYYY',
    isActive: true,
  },
];

export const DROPDOWN_TIME = [
  {
    order: 0,
    id: 'hour',
    label: 'Hour',
    displayAlias: '',
    format: 'hh',
    isActive: false,
  },
  {
    order: 1,
    id: 'minute',
    label: 'Minute',
    displayAlias: '',
    format: 'mm',
    isActive: false,
  },
  {
    order: 2,
    id: 'second',
    label: 'Second',
    displayAlias: '',
    format: 'ss',
    isActive: false,
  },
];

export const LIST_DROPDOWN_SELECT = [
  {
    order: 0,
    id: random(6),
    value: 1,
    label: 'Option 1',
    isPreselect: true,
    goToView: 'none',
  },
];

export const ERROR_MESSAGES = {
  IS_ALREADY: {
    label: getTranslateMessage(translations.isNameAlready.title),
  },
  IS_REQUIRE: {
    label: getTranslateMessage(translations.thisFieldIsRequired.title),
  },
};

export const ARRAY_COUNTRIES = [
  {
    position: '0px -22px',
    countryCode: 'AF',
    url: 'https://countrycode.org/static/images/flagssprite_small_970da81.png',
    country: 'Afghanistan',
    code: '93',
    isoCode: 'AF / AFG',
  },
  {
    position: '0px -55px',
    countryCode: 'AL',
    country: 'Albania',
    code: '355',
    isoCode: 'AL / ALB',
  },
  {
    position: '-16px -77px',
    countryCode: 'DZ',
    country: 'Algeria',
    code: '213',
    isoCode: 'DZ / DZA',
  },
  {
    position: '0px -110px',
    countryCode: 'AS',
    country: 'American Samoa',
    code: '1-684',
    isoCode: 'AS / ASM',
  },
  {
    position: '0px 0px',
    countryCode: 'AD',
    country: 'Andorra',
    code: '376',
    isoCode: 'AD / AND',
  },
  {
    position: '0px -88px',
    countryCode: 'AO',
    country: 'Angola',
    code: '244',
    isoCode: 'AO / AGO',
  },
  {
    position: '0px -44px',
    countryCode: 'AI',
    country: 'Anguilla',
    code: '1-264',
    isoCode: 'AI / AIA',
  },
  {
    position: '0% 0%',
    countryCode: 'AQ',
    country: 'Antarctica',
    code: '672',
    isoCode: 'AQ / ATA',
  },
  {
    position: '0px -33px',
    countryCode: 'AG',
    country: 'Antigua and Barbuda',
    code: '1-268',
    isoCode: 'AG / ATG',
  },
  {
    position: '0px -99px',
    countryCode: 'AR',
    country: 'Argentina',
    code: '54',
    isoCode: 'AR / ARG',
  },
  {
    position: '0px -66px',
    countryCode: 'AM',
    country: 'Armenia',
    code: '374',
    isoCode: 'AM / ARM',
  },
  {
    position: '0px -143px',
    countryCode: 'AW',
    country: 'Aruba',
    code: '297',
    isoCode: 'AW / ABW',
  },
  {
    position: '0px -132px',
    countryCode: 'AU',
    country: 'Australia',
    code: '61',
    isoCode: 'AU / AUS',
  },
  {
    position: '0px -121px',
    countryCode: 'AT',
    country: 'Austria',
    code: '43',
    isoCode: 'AT / AUT',
  },
  {
    position: '0px -165px',
    countryCode: 'AZ',
    country: 'Azerbaijan',
    code: '994',
    isoCode: 'AZ / AZE',
  },
  {
    position: '0px -319px',
    countryCode: 'BS',
    country: 'Bahamas',
    code: '1-242',
    isoCode: 'BS / BHS',
  },
  {
    position: '0px -242px',
    countryCode: 'BH',
    country: 'Bahrain',
    code: '973',
    isoCode: 'BH / BHR',
  },
  {
    position: '0px -198px',
    countryCode: 'BD',
    country: 'Bangladesh',
    code: '880',
    isoCode: 'BD / BGD',
  },
  {
    position: '0px -187px',
    countryCode: 'BB',
    country: 'Barbados',
    code: '1-246',
    isoCode: 'BB / BRB',
  },
  {
    position: '0px -363px',
    countryCode: 'BY',
    country: 'Belarus',
    code: '375',
    isoCode: 'BY / BLR',
  },
  {
    position: '0px -209px',
    countryCode: 'BE',
    country: 'Belgium',
    code: '32',
    isoCode: 'BE / BEL',
  },
  {
    position: '0px -374px',
    countryCode: 'BZ',
    country: 'Belize',
    code: '501',
    isoCode: 'BZ / BLZ',
  },
  {
    position: '0px -264px',
    countryCode: 'BJ',
    country: 'Benin',
    code: '229',
    isoCode: 'BJ / BEN',
  },
  {
    position: '0px -275px',
    countryCode: 'BM',
    country: 'Bermuda',
    code: '1-441',
    isoCode: 'BM / BMU',
  },
  {
    position: '0px -330px',
    countryCode: 'BT',
    country: 'Bhutan',
    code: '975',
    isoCode: 'BT / BTN',
  },
  {
    position: '0px -297px',
    countryCode: 'BO',
    country: 'Bolivia',
    code: '591',
    isoCode: 'BO / BOL',
  },
  {
    position: '0px -176px',
    countryCode: 'BA',
    country: 'Bosnia and Herzegovina',
    code: '387',
    isoCode: 'BA / BIH',
  },
  {
    position: '0px -352px',
    countryCode: 'BW',
    country: 'Botswana',
    code: '267',
    isoCode: 'BW / BWA',
  },
  {
    position: '0px -308px',
    countryCode: 'BR',
    country: 'Brazil',
    code: '55',
    isoCode: 'BR / BRA',
  },
  {
    position: '-16px -572px',
    countryCode: 'IO',
    country: 'British Indian Ocean Territory',
    code: '246',
    isoCode: 'IO / IOT',
  },
  {
    position: '-64px -253px',
    countryCode: 'VG',
    country: 'British Virgin Islands',
    code: '1-284',
    isoCode: 'VG / VGB',
  },
  {
    position: '0px -286px',
    countryCode: 'BN',
    country: 'Brunei',
    code: '673',
    isoCode: 'BN / BRN',
  },
  {
    position: '0px -231px',
    countryCode: 'BG',
    country: 'Bulgaria',
    code: '359',
    isoCode: 'BG / BGR',
  },
  {
    position: '0px -220px',
    countryCode: 'BF',
    country: 'Burkina Faso',
    code: '226',
    isoCode: 'BF / BFA',
  },
  {
    position: '0px -253px',
    countryCode: 'BI',
    country: 'Burundi',
    code: '257',
    isoCode: 'BI / BDI',
  },
  {
    position: '-32px -99px',
    countryCode: 'KH',
    country: 'Cambodia',
    code: '855',
    isoCode: 'KH / KHM',
  },
  {
    position: '0px -495px',
    countryCode: 'CM',
    country: 'Cameroon',
    code: '237',
    isoCode: 'CM / CMR',
  },
  {
    position: '0px -385px',
    countryCode: 'CA',
    country: 'Canada',
    code: '1',
    isoCode: 'CA / CAN',
  },
  {
    position: '0px -561px',
    countryCode: 'CV',
    country: 'Cape Verde',
    code: '238',
    isoCode: 'CV / CPV',
  },
  {
    position: '-32px -176px',
    countryCode: 'KY',
    country: 'Cayman Islands',
    code: '1-345',
    isoCode: 'KY / CYM',
  },
  {
    position: '0px -429px',
    countryCode: 'CF',
    country: 'Central African Republic',
    code: '236',
    isoCode: 'CF / CAF',
  },
  {
    position: '-48px -572px',
    countryCode: 'TD',
    country: 'Chad',
    code: '235',
    isoCode: 'TD / TCD',
  },
  {
    position: '0px -484px',
    countryCode: 'CL',
    country: 'Chile',
    code: '56',
    isoCode: 'CL / CHL',
  },
  {
    position: '0px -506px',
    countryCode: 'CN',
    country: 'China',
    code: '86',
    isoCode: 'CN / CHN',
  },
  {
    position: '0px -572px',
    countryCode: 'CX',
    country: 'Christmas Island',
    code: '61',
    isoCode: 'CX / CXR',
  },
  {
    position: '0px -407px',
    countryCode: 'CC',
    country: 'Cocos Islands',
    code: '61',
    isoCode: 'CC / CCK',
  },
  {
    position: '0px -517px',
    countryCode: 'CO',
    country: 'Colombia',
    code: '57',
    isoCode: 'CO / COL',
  },
  {
    position: '-32px -121px',
    countryCode: 'KM',
    country: 'Comoros',
    code: '269',
    isoCode: 'KM / COM',
  },
  {
    position: '0px -473px',
    countryCode: 'CK',
    country: 'Cook Islands',
    code: '682',
    isoCode: 'CK / COK',
  },
  {
    position: '0px -539px',
    countryCode: 'CR',
    country: 'Costa Rica',
    code: '506',
    isoCode: 'CR / CRI',
  },
  {
    position: '-16px -495px',
    countryCode: 'HR',
    country: 'Croatia',
    code: '385',
    isoCode: 'HR / HRV',
  },
  {
    position: '0px -528px',
    countryCode: 'CU',
    country: 'Cuba',
    code: '53',
    isoCode: 'CU / CUB',
  },
  {
    position: '0% 0%',
    countryCode: 'CW',
    country: 'Curacao',
    code: '599',
    isoCode: 'CW / CUW',
  },
  {
    position: '-16px 0px',
    countryCode: 'CY',
    country: 'Cyprus',
    code: '357',
    isoCode: 'CY / CYP',
  },
  {
    position: '-16px -11px',
    countryCode: 'CZ',
    country: 'Czech Republic',
    code: '420',
    isoCode: 'CZ / CZE',
  },
  {
    position: '0px -418px',
    countryCode: 'CD',
    country: 'Democratic Republic of the Congo',
    code: '243',
    isoCode: 'CD / COD',
  },
  {
    position: '-16px -44px',
    countryCode: 'DK',
    country: 'Denmark',
    code: '45',
    isoCode: 'DK / DNK',
  },
  {
    position: '-16px -33px',
    countryCode: 'DJ',
    country: 'Djibouti',
    code: '253',
    isoCode: 'DJ / DJI',
  },
  {
    position: '-16px -55px',
    countryCode: 'DM',
    country: 'Dominica',
    code: '1-767',
    isoCode: 'DM / DMA',
  },
  {
    position: '-16px -66px',
    countryCode: 'DO',
    country: 'Dominican Republic',
    code: '1-809, 1-829, 1-849',
    isoCode: 'DO / DOM',
  },
  {
    position: '-64px -55px',
    countryCode: 'TL',
    country: 'East Timor',
    code: '670',
    isoCode: 'TL / TLS',
  },
  {
    position: '-16px -88px',
    countryCode: 'EC',
    country: 'Ecuador',
    code: '593',
    isoCode: 'EC / ECU',
  },
  {
    position: '-16px -110px',
    countryCode: 'EG',
    country: 'Egypt',
    code: '20',
    isoCode: 'EG / EGY',
  },
  {
    position: '-48px -528px',
    countryCode: 'SV',
    country: 'El Salvador',
    code: '503',
    isoCode: 'SV / SLV',
  },
  {
    position: '-16px -385px',
    countryCode: 'GQ',
    country: 'Equatorial Guinea',
    code: '240',
    isoCode: 'GQ / GNQ',
  },
  {
    position: '-16px -143px',
    countryCode: 'ER',
    country: 'Eritrea',
    code: '291',
    isoCode: 'ER / ERI',
  },
  {
    position: '-16px -99px',
    countryCode: 'EE',
    country: 'Estonia',
    code: '372',
    isoCode: 'EE / EST',
  },
  {
    position: '-16px -165px',
    countryCode: 'ET',
    country: 'Ethiopia',
    code: '251',
    isoCode: 'ET / ETH',
  },
  {
    position: '-16px -220px',
    countryCode: 'FK',
    country: 'Falkland Islands',
    code: '500',
    isoCode: 'FK / FLK',
  },
  {
    position: '-16px -242px',
    countryCode: 'FO',
    country: 'Faroe Islands',
    code: '298',
    isoCode: 'FO / FRO',
  },
  {
    position: '-16px -209px',
    countryCode: 'FJ',
    country: 'Fiji',
    code: '679',
    isoCode: 'FJ / FJI',
  },
  {
    position: '-16px -198px',
    countryCode: 'FI',
    country: 'Finland',
    code: '358',
    isoCode: 'FI / FIN',
  },
  {
    position: '-16px -253px',
    countryCode: 'FR',
    country: 'France',
    code: '33',
    isoCode: 'FR / FRA',
  },
  {
    position: '-48px -143px',
    countryCode: 'PF',
    country: 'French Polynesia',
    code: '689',
    isoCode: 'PF / PYF',
  },
  {
    position: '-16px -264px',
    countryCode: 'GA',
    country: 'Gabon',
    code: '241',
    isoCode: 'GA / GAB',
  },
  {
    position: '-16px -352px',
    countryCode: 'GM',
    country: 'Gambia',
    code: '220',
    isoCode: 'GM / GMB',
  },
  {
    position: '-16px -297px',
    countryCode: 'GE',
    country: 'Georgia',
    code: '995',
    isoCode: 'GE / GEO',
  },
  {
    position: '-16px -22px',
    countryCode: 'DE',
    country: 'Germany',
    code: '49',
    isoCode: 'DE / DEU',
  },
  {
    position: '-16px -319px',
    countryCode: 'GH',
    country: 'Ghana',
    code: '233',
    isoCode: 'GH / GHA',
  },
  {
    position: '-16px -330px',
    countryCode: 'GI',
    country: 'Gibraltar',
    code: '350',
    isoCode: 'GI / GIB',
  },
  {
    position: '-16px -396px',
    countryCode: 'GR',
    country: 'Greece',
    code: '30',
    isoCode: 'GR / GRC',
  },
  {
    position: '-16px -341px',
    countryCode: 'GL',
    country: 'Greenland',
    code: '299',
    isoCode: 'GL / GRL',
  },
  {
    position: '-16px -286px',
    countryCode: 'GD',
    country: 'Grenada',
    code: '1-473',
    isoCode: 'GD / GRD',
  },
  {
    position: '-16px -429px',
    countryCode: 'GU',
    country: 'Guam',
    code: '1-671',
    isoCode: 'GU / GUM',
  },
  {
    position: '-16px -418px',
    countryCode: 'GT',
    country: 'Guatemala',
    code: '502',
    isoCode: 'GT / GTM',
  },
  {
    position: '0% 0%',
    countryCode: 'GG',
    country: 'Guernsey',
    code: '44-1481',
    isoCode: 'GG / GGY',
  },
  {
    position: '-16px -363px',
    countryCode: 'GN',
    country: 'Guinea',
    code: '224',
    isoCode: 'GN / GIN',
  },
  {
    position: '-16px -440px',
    countryCode: 'GW',
    country: 'Guinea-Bissau',
    code: '245',
    isoCode: 'GW / GNB',
  },
  {
    position: '-16px -451px',
    countryCode: 'GY',
    country: 'Guyana',
    code: '592',
    isoCode: 'GY / GUY',
  },
  {
    position: '-16px -506px',
    countryCode: 'HT',
    country: 'Haiti',
    code: '509',
    isoCode: 'HT / HTI',
  },
  {
    position: '-16px -484px',
    countryCode: 'HN',
    country: 'Honduras',
    code: '504',
    isoCode: 'HN / HND',
  },
  {
    position: '-16px -462px',
    countryCode: 'HK',
    country: 'Hong Kong',
    code: '852',
    isoCode: 'HK / HKG',
  },
  {
    position: '-16px -517px',
    countryCode: 'HU',
    country: 'Hungary',
    code: '36',
    isoCode: 'HU / HUN',
  },
  {
    position: '-32px -22px',
    countryCode: 'IS',
    country: 'Iceland',
    code: '354',
    isoCode: 'IS / ISL',
  },
  {
    position: '-16px -561px',
    countryCode: 'IN',
    country: 'India',
    code: '91',
    isoCode: 'IN / IND',
  },
  {
    position: '-16px -528px',
    countryCode: 'ID',
    country: 'Indonesia',
    code: '62',
    isoCode: 'ID / IDN',
  },
  {
    position: '-32px -11px',
    countryCode: 'IR',
    country: 'Iran',
    code: '98',
    isoCode: 'IR / IRN',
  },
  {
    position: '-32px 0px',
    countryCode: 'IQ',
    country: 'Iraq',
    code: '964',
    isoCode: 'IQ / IRQ',
  },
  {
    position: '-16px -539px',
    countryCode: 'IE',
    country: 'Ireland',
    code: '353',
    isoCode: 'IE / IRL',
  },
  {
    position: '0% 0%',
    countryCode: 'IM',
    country: 'Isle of Man',
    code: '44-1624',
    isoCode: 'IM / IMN',
  },
  {
    position: '-16px -550px',
    countryCode: 'IL',
    country: 'Israel',
    code: '972',
    isoCode: 'IL / ISR',
  },
  {
    position: '-32px -33px',
    countryCode: 'IT',
    country: 'Italy',
    code: '39',
    isoCode: 'IT / ITA',
  },
  {
    position: '0px -462px',
    countryCode: 'CI',
    country: 'Ivory Coast',
    code: '225',
    isoCode: 'CI / CIV',
  },
  {
    position: '-32px -44px',
    countryCode: 'JM',
    country: 'Jamaica',
    code: '1-876',
    isoCode: 'JM / JAM',
  },
  {
    position: '-32px -66px',
    countryCode: 'JP',
    country: 'Japan',
    code: '81',
    isoCode: 'JP / JPN',
  },
  {
    position: '0% 0%',
    countryCode: 'JE',
    country: 'Jersey',
    code: '44-1534',
    isoCode: 'JE / JEY',
  },
  {
    position: '-32px -55px',
    countryCode: 'JO',
    country: 'Jordan',
    code: '962',
    isoCode: 'JO / JOR',
  },
  {
    position: '-32px -187px',
    countryCode: 'KZ',
    country: 'Kazakhstan',
    code: '7',
    isoCode: 'KZ / KAZ',
  },
  {
    position: '-32px -77px',
    countryCode: 'KE',
    country: 'Kenya',
    code: '254',
    isoCode: 'KE / KEN',
  },
  {
    position: '-32px -110px',
    countryCode: 'KI',
    country: 'Kiribati',
    code: '686',
    isoCode: 'KI / KIR',
  },
  {
    position: '0% 0%',
    countryCode: 'XK',
    country: 'Kosovo',
    code: '383',
    isoCode: 'XK / XKX',
  },
  {
    position: '-32px -165px',
    countryCode: 'KW',
    country: 'Kuwait',
    code: '965',
    isoCode: 'KW / KWT',
  },
  {
    position: '-32px -88px',
    countryCode: 'KG',
    country: 'Kyrgyzstan',
    code: '996',
    isoCode: 'KG / KGZ',
  },
  {
    position: '-32px -198px',
    countryCode: 'LA',
    country: 'Laos',
    code: '856',
    isoCode: 'LA / LAO',
  },
  {
    position: '-32px -297px',
    countryCode: 'LV',
    country: 'Latvia',
    code: '371',
    isoCode: 'LV / LVA',
  },
  {
    position: '-32px -209px',
    countryCode: 'LB',
    country: 'Lebanon',
    code: '961',
    isoCode: 'LB / LBN',
  },
  {
    position: '-32px -264px',
    countryCode: 'LS',
    country: 'Lesotho',
    code: '266',
    isoCode: 'LS / LSO',
  },
  {
    position: '-32px -242px',
    countryCode: 'LR',
    country: 'Liberia',
    code: '231',
    isoCode: 'LR / LBR',
  },
  {
    position: '-32px -308px',
    countryCode: 'LY',
    country: 'Libya',
    code: '218',
    isoCode: 'LY / LBY',
  },
  {
    position: '-32px -231px',
    countryCode: 'LI',
    country: 'Liechtenstein',
    code: '423',
    isoCode: 'LI / LIE',
  },
  {
    position: '-32px -275px',
    countryCode: 'LT',
    country: 'Lithuania',
    code: '370',
    isoCode: 'LT / LTU',
  },
  {
    position: '-32px -286px',
    countryCode: 'LU',
    country: 'Luxembourg',
    code: '352',
    isoCode: 'LU / LUX',
  },
  {
    position: '-32px -429px',
    countryCode: 'MO',
    country: 'Macau',
    code: '853',
    isoCode: 'MO / MAC',
  },
  {
    position: '-32px -385px',
    countryCode: 'MK',
    country: 'Macedonia',
    code: '389',
    isoCode: 'MK / MKD',
  },
  {
    position: '-32px -363px',
    countryCode: 'MG',
    country: 'Madagascar',
    code: '261',
    isoCode: 'MG / MDG',
  },
  {
    position: '-32px -517px',
    countryCode: 'MW',
    country: 'Malawi',
    code: '265',
    isoCode: 'MW / MWI',
  },
  {
    position: '-32px -539px',
    countryCode: 'MY',
    country: 'Malaysia',
    code: '60',
    isoCode: 'MY / MYS',
  },
  {
    position: '-32px -506px',
    countryCode: 'MV',
    country: 'Maldives',
    code: '960',
    isoCode: 'MV / MDV',
  },
  {
    position: '-32px -396px',
    countryCode: 'ML',
    country: 'Mali',
    code: '223',
    isoCode: 'ML / MLI',
  },
  {
    position: '-32px -484px',
    countryCode: 'MT',
    country: 'Malta',
    code: '356',
    isoCode: 'MT / MLT',
  },
  {
    position: '-32px -374px',
    countryCode: 'MH',
    country: 'Marshall Islands',
    code: '692',
    isoCode: 'MH / MHL',
  },
  {
    position: '-32px -462px',
    countryCode: 'MR',
    country: 'Mauritania',
    code: '222',
    isoCode: 'MR / MRT',
  },
  {
    position: '-32px -495px',
    countryCode: 'MU',
    country: 'Mauritius',
    code: '230',
    isoCode: 'MU / MUS',
  },
  {
    position: '-64px -341px',
    countryCode: 'YT',
    country: 'Mayotte',
    code: '262',
    isoCode: 'YT / MYT',
  },
  {
    position: '-32px -528px',
    countryCode: 'MX',
    country: 'Mexico',
    code: '52',
    isoCode: 'MX / MEX',
  },
  {
    position: '-16px -231px',
    countryCode: 'FM',
    country: 'Micronesia',
    code: '691',
    isoCode: 'FM / FSM',
  },
  {
    position: '-32px -341px',
    countryCode: 'MD',
    country: 'Moldova',
    code: '373',
    isoCode: 'MD / MDA',
  },
  {
    position: '-32px -330px',
    countryCode: 'MC',
    country: 'Monaco',
    code: '377',
    isoCode: 'MC / MCO',
  },
  {
    position: '-32px -418px',
    countryCode: 'MN',
    country: 'Mongolia',
    code: '976',
    isoCode: 'MN / MNG',
  },
  {
    position: '-32px -352px',
    countryCode: 'ME',
    country: 'Montenegro',
    code: '382',
    isoCode: 'ME / MNE',
  },
  {
    position: '-32px -473px',
    countryCode: 'MS',
    country: 'Montserrat',
    code: '1-664',
    isoCode: 'MS / MSR',
  },
  {
    position: '-32px -319px',
    countryCode: 'MA',
    country: 'Morocco',
    code: '212',
    isoCode: 'MA / MAR',
  },
  {
    position: '-32px -550px',
    countryCode: 'MZ',
    country: 'Mozambique',
    code: '258',
    isoCode: 'MZ / MOZ',
  },
  {
    position: '-32px -407px',
    countryCode: 'MM',
    country: 'Myanmar',
    code: '95',
    isoCode: 'MM / MMR',
  },
  {
    position: '-32px -561px',
    countryCode: 'NA',
    country: 'Namibia',
    code: '264',
    isoCode: 'NA / NAM',
  },
  {
    position: '-48px -77px',
    countryCode: 'NR',
    country: 'Nauru',
    code: '674',
    isoCode: 'NR / NRU',
  },
  {
    position: '-48px -66px',
    countryCode: 'NP',
    country: 'Nepal',
    code: '977',
    isoCode: 'NP / NPL',
  },
  {
    position: '-48px -44px',
    countryCode: 'NL',
    country: 'Netherlands',
    code: '31',
    isoCode: 'NL / NLD',
  },
  {
    position: '0px -77px',
    countryCode: 'AN',
    country: 'Netherlands Antilles',
    code: '599',
    isoCode: 'AN / ANT',
  },
  {
    position: '-32px -572px',
    countryCode: 'NC',
    country: 'New Caledonia',
    code: '687',
    isoCode: 'NC / NCL',
  },
  {
    position: '-48px -99px',
    countryCode: 'NZ',
    country: 'New Zealand',
    code: '64',
    isoCode: 'NZ / NZL',
  },
  {
    position: '-48px -33px',
    countryCode: 'NI',
    country: 'Nicaragua',
    code: '505',
    isoCode: 'NI / NIC',
  },
  {
    position: '-48px 0px',
    countryCode: 'NE',
    country: 'Niger',
    code: '227',
    isoCode: 'NE / NER',
  },
  {
    position: '-48px -22px',
    countryCode: 'NG',
    country: 'Nigeria',
    code: '234',
    isoCode: 'NG / NGA',
  },
  {
    position: '-48px -88px',
    countryCode: 'NU',
    country: 'Niue',
    code: '683',
    isoCode: 'NU / NIU',
  },
  {
    position: '-32px -143px',
    countryCode: 'KP',
    country: 'North Korea',
    code: '850',
    isoCode: 'KP / PRK',
  },
  {
    position: '-32px -440px',
    countryCode: 'MP',
    country: 'Northern Mariana Islands',
    code: '1-670',
    isoCode: 'MP / MNP',
  },
  {
    position: '-48px -55px',
    countryCode: 'NO',
    country: 'Norway',
    code: '47',
    isoCode: 'NO / NOR',
  },
  {
    position: '-48px -110px',
    countryCode: 'OM',
    country: 'Oman',
    code: '968',
    isoCode: 'OM / OMN',
  },
  {
    position: '-48px -176px',
    countryCode: 'PK',
    country: 'Pakistan',
    code: '92',
    isoCode: 'PK / PAK',
  },
  {
    position: '-48px -253px',
    countryCode: 'PW',
    country: 'Palau',
    code: '680',
    isoCode: 'PW / PLW',
  },
  {
    position: '-48px -231px',
    countryCode: 'PS',
    country: 'Palestine',
    code: '970',
    isoCode: 'PS / PSE',
  },
  {
    position: '-48px -121px',
    countryCode: 'PA',
    country: 'Panama',
    code: '507',
    isoCode: 'PA / PAN',
  },
  {
    position: '-48px -154px',
    countryCode: 'PG',
    country: 'Papua New Guinea',
    code: '675',
    isoCode: 'PG / PNG',
  },
  {
    position: '-48px -264px',
    countryCode: 'PY',
    country: 'Paraguay',
    code: '595',
    isoCode: 'PY / PRY',
  },
  {
    position: '-48px -132px',
    countryCode: 'PE',
    country: 'Peru',
    code: '51',
    isoCode: 'PE / PER',
  },
  {
    position: '-48px -165px',
    countryCode: 'PH',
    country: 'Philippines',
    code: '63',
    isoCode: 'PH / PHL',
  },
  {
    position: '-48px -209px',
    countryCode: 'PN',
    country: 'Pitcairn',
    code: '64',
    isoCode: 'PN / PCN',
  },
  {
    position: '-48px -187px',
    countryCode: 'PL',
    country: 'Poland',
    code: '48',
    isoCode: 'PL / POL',
  },
  {
    position: '-48px -242px',
    countryCode: 'PT',
    country: 'Portugal',
    code: '351',
    isoCode: 'PT / PRT',
  },
  {
    position: '-48px -220px',
    countryCode: 'PR',
    country: 'Puerto Rico',
    code: '1-787, 1-939',
    isoCode: 'PR / PRI',
  },
  {
    position: '-48px -275px',
    countryCode: 'QA',
    country: 'Qatar',
    code: '974',
    isoCode: 'QA / QAT',
  },
  {
    position: '0px -440px',
    countryCode: 'CG',
    country: 'Republic of the Congo',
    code: '242',
    isoCode: 'CG / COG',
  },
  {
    position: '-48px -286px',
    countryCode: 'RE',
    country: 'Reunion',
    code: '262',
    isoCode: 'RE / REU',
  },
  {
    position: '-48px -297px',
    countryCode: 'RO',
    country: 'Romania',
    code: '40',
    isoCode: 'RO / ROU',
  },
  {
    position: '-48px -319px',
    countryCode: 'RU',
    country: 'Russia',
    code: '7',
    isoCode: 'RU / RUS',
  },
  {
    position: '-48px -330px',
    countryCode: 'RW',
    country: 'Rwanda',
    code: '250',
    isoCode: 'RW / RWA',
  },
  {
    position: '0% 0%',
    countryCode: 'BL',
    country: 'Saint Barthelemy',
    code: '590',
    isoCode: 'BL / BLM',
  },
  {
    position: '-48px -418px',
    countryCode: 'SH',
    country: 'Saint Helena',
    code: '290',
    isoCode: 'SH / SHN',
  },
  {
    position: '-32px -132px',
    countryCode: 'KN',
    country: 'Saint Kitts and Nevis',
    code: '1-869',
    isoCode: 'KN / KNA',
  },
  {
    position: '-32px -220px',
    countryCode: 'LC',
    country: 'Saint Lucia',
    code: '1-758',
    isoCode: 'LC / LCA',
  },
  {
    position: '0% 0%',
    countryCode: 'MF',
    country: 'Saint Martin',
    code: '590',
    isoCode: 'MF / MAF',
  },
  {
    position: '-48px -198px',
    countryCode: 'PM',
    country: 'Saint Pierre and Miquelon',
    code: '508',
    isoCode: 'PM / SPM',
  },
  {
    position: '-64px -231px',
    countryCode: 'VC',
    country: 'Saint Vincent and the Grenadines',
    code: '1-784',
    isoCode: 'VC / VCT',
  },
  {
    position: '-64px -319px',
    countryCode: 'WS',
    country: 'Samoa',
    code: '685',
    isoCode: 'WS / WSM',
  },
  {
    position: '-48px -473px',
    countryCode: 'SM',
    country: 'San Marino',
    code: '378',
    isoCode: 'SM / SMR',
  },
  {
    position: '-48px -517px',
    countryCode: 'ST',
    country: 'Sao Tome and Principe',
    code: '239',
    isoCode: 'ST / STP',
  },
  {
    position: '-48px -341px',
    countryCode: 'SA',
    country: 'Saudi Arabia',
    code: '966',
    isoCode: 'SA / SAU',
  },
  {
    position: '-48px -484px',
    countryCode: 'SN',
    country: 'Senegal',
    code: '221',
    isoCode: 'SN / SEN',
  },
  {
    position: '-48px -308px',
    countryCode: 'RS',
    country: 'Serbia',
    code: '381',
    isoCode: 'RS / SRB',
  },
  {
    position: '-48px -363px',
    countryCode: 'SC',
    country: 'Seychelles',
    code: '248',
    isoCode: 'SC / SYC',
  },
  {
    position: '-48px -462px',
    countryCode: 'SL',
    country: 'Sierra Leone',
    code: '232',
    isoCode: 'SL / SLE',
  },
  {
    position: '-48px -407px',
    countryCode: 'SG',
    country: 'Singapore',
    code: '65',
    isoCode: 'SG / SGP',
  },
  {
    position: '0% 0%',
    countryCode: 'SX',
    country: 'Sint Maarten',
    code: '1-721',
    isoCode: 'SX / SXM',
  },
  {
    position: '-48px -451px',
    countryCode: 'SK',
    country: 'Slovakia',
    code: '421',
    isoCode: 'SK / SVK',
  },
  {
    position: '-48px -429px',
    countryCode: 'SI',
    country: 'Slovenia',
    code: '386',
    isoCode: 'SI / SVN',
  },
  {
    position: '-48px -352px',
    countryCode: 'SB',
    country: 'Solomon Islands',
    code: '677',
    isoCode: 'SB / SLB',
  },
  {
    position: '-48px -495px',
    countryCode: 'SO',
    country: 'Somalia',
    code: '252',
    isoCode: 'SO / SOM',
  },
  {
    position: '-64px -352px',
    countryCode: 'ZA',
    country: 'South Africa',
    code: '27',
    isoCode: 'ZA / ZAF',
  },
  {
    position: '-32px -154px',
    countryCode: 'KR',
    country: 'South Korea',
    code: '82',
    isoCode: 'KR / KOR',
  },
  {
    position: '0% 0%',
    countryCode: 'SS',
    country: 'South Sudan',
    code: '211',
    isoCode: 'SS / SSD',
  },
  {
    position: '-16px -154px',
    countryCode: 'ES',
    country: 'Spain',
    code: '34',
    isoCode: 'ES / ESP',
  },
  {
    position: '-32px -253px',
    countryCode: 'LK',
    country: 'Sri Lanka',
    code: '94',
    isoCode: 'LK / LKA',
  },
  {
    position: '-48px -385px',
    countryCode: 'SD',
    country: 'Sudan',
    code: '249',
    isoCode: 'SD / SDN',
  },
  {
    position: '-48px -506px',
    countryCode: 'SR',
    country: 'Suriname',
    code: '597',
    isoCode: 'SR / SUR',
  },
  {
    position: '-48px -440px',
    countryCode: 'SJ',
    country: 'Svalbard and Jan Mayen',
    code: '47',
    isoCode: 'SJ / SJM',
  },
  {
    position: '-48px -550px',
    countryCode: 'SZ',
    country: 'Swaziland',
    code: '268',
    isoCode: 'SZ / SWZ',
  },
  {
    position: '-48px -396px',
    countryCode: 'SE',
    country: 'Sweden',
    code: '46',
    isoCode: 'SE / SWE',
  },
  {
    position: '0px -451px',
    countryCode: 'CH',
    country: 'Switzerland',
    code: '41',
    isoCode: 'CH / CHE',
  },
  {
    position: '-48px -539px',
    countryCode: 'SY',
    country: 'Syria',
    code: '963',
    isoCode: 'SY / SYR',
  },
  {
    position: '-64px -132px',
    countryCode: 'TW',
    country: 'Taiwan',
    code: '886',
    isoCode: 'TW / TWN',
  },
  {
    position: '-64px -33px',
    countryCode: 'TJ',
    country: 'Tajikistan',
    code: '992',
    isoCode: 'TJ / TJK',
  },
  {
    position: '-64px -143px',
    countryCode: 'TZ',
    country: 'Tanzania',
    code: '255',
    isoCode: 'TZ / TZA',
  },
  {
    position: '-64px -22px',
    countryCode: 'TH',
    country: 'Thailand',
    code: '66',
    isoCode: 'TH / THA',
  },
  {
    position: '-64px -11px',
    countryCode: 'TG',
    country: 'Togo',
    code: '228',
    isoCode: 'TG / TGO',
  },
  {
    position: '-64px -44px',
    countryCode: 'TK',
    country: 'Tokelau',
    code: '690',
    isoCode: 'TK / TKL',
  },
  {
    position: '-64px -88px',
    countryCode: 'TO',
    country: 'Tonga',
    code: '676',
    isoCode: 'TO / TON',
  },
  {
    position: '-64px -110px',
    countryCode: 'TT',
    country: 'Trinidad and Tobago',
    code: '1-868',
    isoCode: 'TT / TTO',
  },
  {
    position: '-64px -77px',
    countryCode: 'TN',
    country: 'Tunisia',
    code: '216',
    isoCode: 'TN / TUN',
  },
  {
    position: '-64px -99px',
    countryCode: 'TR',
    country: 'Turkey',
    code: '90',
    isoCode: 'TR / TUR',
  },
  {
    position: '-64px -66px',
    countryCode: 'TM',
    country: 'Turkmenistan',
    code: '993',
    isoCode: 'TM / TKM',
  },
  {
    position: '-48px -561px',
    countryCode: 'TC',
    country: 'Turks and Caicos Islands',
    code: '1-649',
    isoCode: 'TC / TCA',
  },
  {
    position: '-64px -121px',
    countryCode: 'TV',
    country: 'Tuvalu',
    code: '688',
    isoCode: 'TV / TUV',
  },
  {
    position: '-64px -264px',
    countryCode: 'VI',
    country: 'U.S. Virgin Islands',
    code: '1-340',
    isoCode: 'VI / VIR',
  },
  {
    position: '-64px -165px',
    countryCode: 'UG',
    country: 'Uganda',
    code: '256',
    isoCode: 'UG / UGA',
  },
  {
    position: '-64px -154px',
    countryCode: 'UA',
    country: 'Ukraine',
    code: '380',
    isoCode: 'UA / UKR',
  },
  {
    position: '0px -11px',
    countryCode: 'AE',
    country: 'United Arab Emirates',
    code: '971',
    isoCode: 'AE / ARE',
  },
  {
    position: '-16px -275px',
    countryCode: 'GB',
    country: 'United Kingdom',
    code: '44',
    isoCode: 'GB / GBR',
  },
  {
    position: '-64px -187px',
    countryCode: 'US',
    country: 'United States',
    code: '1',
    isoCode: 'US / USA',
  },
  {
    position: '-64px -198px',
    countryCode: 'UY',
    country: 'Uruguay',
    code: '598',
    isoCode: 'UY / URY',
  },
  {
    position: '-64px -209px',
    countryCode: 'UZ',
    country: 'Uzbekistan',
    code: '998',
    isoCode: 'UZ / UZB',
  },
  {
    position: '-64px -286px',
    countryCode: 'VU',
    country: 'Vanuatu',
    code: '678',
    isoCode: 'VU / VUT',
  },
  {
    position: '-64px -220px',
    countryCode: 'VA',
    country: 'Vatican',
    code: '379',
    isoCode: 'VA / VAT',
  },
  {
    position: '-64px -242px',
    countryCode: 'VE',
    country: 'Venezuela',
    code: '58',
    isoCode: 'VE / VEN',
  },
  {
    position: '-64px -275px',
    countryCode: 'VN',
    country: 'Vietnam',
    code: '84',
    isoCode: 'VN / VNM',
  },
  {
    position: '-64px -308px',
    countryCode: 'WF',
    country: 'Wallis and Futuna',
    code: '681',
    isoCode: 'WF / WLF',
  },
  {
    position: '-16px -121px',
    countryCode: 'EH',
    country: 'Western Sahara',
    code: '212',
    isoCode: 'EH / ESH',
  },
  {
    position: '-64px -330px',
    countryCode: 'YE',
    country: 'Yemen',
    code: '967',
    isoCode: 'YE / YEM',
  },
  {
    position: '-64px -363px',
    countryCode: 'ZM',
    country: 'Zambia',
    code: '260',
    isoCode: 'ZM / ZMB',
  },
  {
    position: '-64px -374px',
    countryCode: 'ZW',
    country: 'Zimbabwe',
    code: '263',
    isoCode: 'ZW / ZWE',
  },
];

export const COUNTRY_OPTIONS = formatCodeCountry(ARRAY_COUNTRIES);

export const FIELD_NAMES = {
  'Name field': 'name',
  'Email field': 'email',
  'Last Name field': 'Last Name',
  'First Name field': 'First Name',
};
