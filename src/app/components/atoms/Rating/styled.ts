import styled from 'styled-components';

export const RatingWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;

  .rating-stars {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .rating-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &--half {
      margin-left: -2px;
    }
  }

  .rating-icon {
    display: inline-block;
    color: #d9d9d9;
    transition: color 0.2s ease;

    &--filled {
      color: #faad14;
    }

    &--half {
      background: linear-gradient(90deg, #faad14 50%, #d9d9d9 50%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    &--small {
      font-size: 16px;
    }

    &--medium {
      font-size: 20px;
    }

    &--large {
      font-size: 24px;
    }
  }

  .rating-text {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
  }

  &.rating-component--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  /* Hover effects */
  .rating-item:hover .rating-icon {
    transform: scale(1.1);
  }

  /* Heart specific styles */
  .rating-icon:contains('♥') {
    &--filled {
      color: #ff4d4f;
    }
  }

  /* Thumb specific styles */
  .rating-icon:contains('👍') {
    &--filled {
      filter: brightness(1.2);
    }
  }
`;
