import { translations } from 'locales/translations';
import { getTranslateMessage } from 'utils/messages';

export const RATING_TYPE = {
  STAR: 'star',
  EMOTION: 'emotion',
  YESNO: 'yesno',
} as const;

export const RATING_OPTIONS_BY_TYPE = {
  [RATING_TYPE.EMOTION]: [
    {
      score: 1,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.extremelyDissatisfied),
      value: '1',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 2,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.veryDissatisfied),
      value: '2',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 3,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.dissatisfied),
      value: '3',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 4,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.slightlyDissatisfied),
      value: '4',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 5,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.neutral),
      value: '5',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 6,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.slightlySatisfied),
      value: '6',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 7,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.satisfied),
      value: '7',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 8,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.verySatisfied),
      value: '8',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 9,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.extremelySatisfied),
      value: '9',
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 10,
      label: getTranslateMessage(translations.optinFields.fields.rating.emotion.delighted),
      value: '10',
      isPreselect: true,
      goToView: 'none',
    },
  ],
  [RATING_TYPE.STAR]: [
    {
      score: 1,
      value: '1',
      label: getTranslateMessage(translations.optinFields.fields.rating.star.poor),
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 2,
      value: '2',
      label: getTranslateMessage(translations.optinFields.fields.rating.star.fair),
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 3,
      value: '3',
      label: getTranslateMessage(translations.optinFields.fields.rating.star.good),
      isPreselect: true,
      goToView: 'none',
    },
    {
      score: 4,
      value: '4',
      label: getTranslateMessage(translations.optinFields.fields.rating.star.veryGood),
      isPreselect: false,
      goToView: 'none',
    },
    {
      score: 5,
      value: '5',
      label: getTranslateMessage(translations.optinFields.fields.rating.star.excellent),
      isPreselect: false,
      goToView: 'none',
    },
  ],
  [RATING_TYPE.YESNO]: [
    {
      score: 1,
      value: '1',
      label: getTranslateMessage(translations.optinFields.fields.rating.yesno.yes),
      isPreselect: true,
      goToView: 'none',
    },
    {
      score: 0,
      value: '0',
      label: getTranslateMessage(translations.optinFields.fields.rating.yesno.no),
      isPreselect: false,
      goToView: 'none',
    },
  ],
} as const;
