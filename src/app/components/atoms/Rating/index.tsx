// Libraries
import React, { CSSProperties, useState } from 'react';
import classNames from 'classnames';

// Styled
import { RatingWrapper } from './styled';
import {
  EmotionLevel1Icon,
  EmotionLevel2Icon,
  EmotionLevel3Icon,
  EmotionLevel4Icon,
  EmotionLevel5Icon,
  EmotionLevel6Icon,
  EmotionLevel7Icon,
  EmotionLevel8Icon,
  EmotionLevel9Icon,
  NoIcon,
  StarIcon,
  YesIcon,
} from './icons';

interface RatingOption {
  id: string;
  value: number;
  label: string;
  isPreselect: boolean;
  goToView: string;
}

interface RatingProps {
  value?: number;
  ratingType?: 'star' | 'emotion' | 'yesno';
  ratingOptions?: RatingOption[];
  alignment?: 'left' | 'center' | 'right';
  size?: number;
  optionColumn?: number;
  optionPosition?: 'top' | 'right' | 'bottom' | 'left';
  optionGap?: CSSProperties['gap'];
  borderStyle?: 'solid' | 'dashed' | 'dotted' | 'none';
  borderColor?: string;
  bgBeforeColor?: string;
  bgAfterColor?: string;
  onChange?: (value: number) => void;
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

export const Rating: React.FC<RatingProps> = props => {
  const {
    value = 0,
    ratingType = 'star',
    ratingOptions = [],
    alignment = 'left',
    size = 30,
    optionColumn = 5,
    optionGap = '5px',
    optionPosition = 'left',
    borderStyle = 'solid',
    borderColor = '#F7DA64',
    bgBeforeColor = '#FFFFFF',
    bgAfterColor = '#F7DA64',
    onChange,
    disabled = false,
    className,
    id,
    name,
    style,
    onClick,
  } = props;

  const [hoverValue, setHoverValue] = useState<number>(0);

  const handleClick = (optionValue: number) => {
    if (!disabled && onChange) {
      onChange(optionValue);
    }
  };

  const handleMouseEnter = (optionValue: number) => {
    if (!disabled) {
      setHoverValue(optionValue);
    }
  };

  const handleMouseLeave = () => {
    if (!disabled) {
      setHoverValue(0);
    }
  };

  const getRatingIcon = (
    option: RatingOption,
    idx: number,
    others: {
      isSelected: boolean;
      isHovered: boolean;
    },
  ) => {
    const { isSelected, isHovered } = others;

    const iconClass = classNames('rating-icon', {
      'rating-icon--selected': isSelected,
      'rating-icon--hovered': isHovered,
    });

    switch (ratingType) {
      case 'emotion':
        const isPreselected = option.isPreselect;

        const EmotionIcon = {
          1: EmotionLevel1Icon,
          2: EmotionLevel2Icon,
          3: EmotionLevel3Icon,
          4: EmotionLevel4Icon,
          5: EmotionLevel5Icon,
          6: EmotionLevel6Icon,
          7: EmotionLevel7Icon,
          8: EmotionLevel8Icon,
          9: EmotionLevel9Icon,
        }[value];

        if (!EmotionIcon) break;

        return (
          <EmotionIcon
            className={iconClass}
            width={size}
            height={size}
            borderColor={borderColor}
            backgroundColor={isPreselected ? bgAfterColor : bgBeforeColor}
          />
        );
      case 'yesno': {
        const isPreselected = option.isPreselect;

        const YesNoIcon = {
          0: NoIcon,
          1: YesIcon,
        }[value];

        if (!YesNoIcon) break;

        return (
          <YesNoIcon
            width={size}
            height={size}
            className={iconClass}
            borderColor={borderColor}
            backgroundColor={isPreselected ? bgAfterColor : bgBeforeColor}
          />
        );
      }
      case 'star': {
        const preselectIdx = ratingOptions.findIndex(opt => opt.isPreselect);
        const isPreselected = preselectIdx !== -1 && idx <= preselectIdx;

        return (
          <StarIcon
            className={iconClass}
            width={size}
            height={size}
            borderColor={borderColor}
            backgroundColor={isPreselected ? bgAfterColor : bgBeforeColor}
          />
        );
      }
      default:
        return null;
    }
  };

  const containerStyle: React.CSSProperties = {
    ...style,
    textAlign: alignment,
    border: borderStyle !== 'none' ? `1px ${borderStyle} ${borderColor}` : 'none',
    backgroundColor: bgBeforeColor,
    padding: '8px',
    borderRadius: '4px',
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${optionColumn}, 1fr)`,
    gap: '8px',
    justifyItems: alignment,
  };

  return (
    <RatingWrapper
      className={classNames('rating-component', className, {
        'rating-component--disabled': disabled,
      })}
      id={id}
      style={containerStyle}
      onClick={onClick}
    >
      <input type="hidden" name={name} value={value} />

      <div className="rating-options" style={gridStyle}>
        {ratingOptions.map((option, idx) => {
          const isSelected = value === option.value;
          const isHovered = hoverValue === option.value;

          return (
            <div
              key={option.id}
              className={classNames('rating-option', {
                'rating-option--selected': isSelected,
                'rating-option--hovered': isHovered,
              })}
              onClick={() => handleClick(option.value)}
              onMouseEnter={() => handleMouseEnter(option.value)}
              onMouseLeave={handleMouseLeave}
              style={{
                cursor: disabled ? 'default' : 'pointer',
                fontSize: `${size}px`,
                backgroundColor: isSelected ? bgAfterColor : 'transparent',
                padding: '4px 8px',
                borderRadius: '4px',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                gap: optionGap,
                flexDirection: (
                  {
                    left: 'row',
                    right: 'row-reverse',
                    top: 'column',
                    bottom: 'column-reverse',
                  } as const
                )[optionPosition],
              }}
            >
              <div className="rating-label" style={{ fontSize: '12px', marginTop: '4px' }}>
                {option.label}
              </div>

              <div className="rating-icon-wrapper">
                {getRatingIcon(option, idx, {
                  isSelected,
                  isHovered,
                })}
              </div>
            </div>
          );
        })}
      </div>
    </RatingWrapper>
  );
};

export default Rating;
